'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { CountrySelector } from '@/components/ui/country-selector';
import { useAuth } from '@/contexts/AuthContext';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import {
  getProfile,
  updateProfile,
  getInfluencer,
  updateInfluencer,
  getInfluencerPlatforms,
  getInfluencerCategories,
} from '@/lib/profiles';
import { supabase } from '@/lib/supabase';
import {
  Loader2,
  Save,
  User,
  Globe,
  Package,
  Eye,
  Instagram,
  Youtube,
  Plus,
  Crown,
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import {
  getInfluencerPackages,
  type PricingPackage,
} from '@/lib/pricing-packages';
import { AvatarUpload } from '@/components/profile/AvatarUpload';

// TikTok icon component since it's not in Lucide
const TikTokIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z" />
  </svg>
);
import { InfluencerCard } from '@/components/marketplace/InfluencerCard';
import type { InfluencerSearchResult } from '@/lib/marketplace';
import { UpgradeRequiredModal } from '@/components/modals/UpgradeRequiredModal';
import { InfluencerSubscriptionDebugger } from '@/components/debug/InfluencerSubscriptionDebugger';

const profileSchema = z.object({
  username: z.string().min(3, 'Username mora imati najmanje 3 karaktera'),
  public_display_name: z
    .string()
    .max(100, 'Ime može imati maksimalno 100 karaktera')
    .optional(),
  bio: z
    .string()
    .max(500, 'Bio može imati maksimalno 500 karaktera')
    .optional(),
  location: z.string().optional(),
  country: z.string().optional(),
  instagram_handle: z.string().optional(),
  instagram_followers: z.number().min(0).optional(),
  tiktok_handle: z.string().optional(),
  tiktok_followers: z.number().min(0).optional(),
  youtube_handle: z.string().optional(),
  youtube_subscribers: z.number().min(0).optional(),
});

type ProfileForm = z.infer<typeof profileSchema>;

export default function InfluencerProfilePage() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [profile, setProfile] = useState<any>(null);
  const [packages, setPackages] = useState<PricingPackage[]>([]);
  const [packagesLoading, setPackagesLoading] = useState(true);
  const [customOffersEnabled, setCustomOffersEnabled] = useState(false);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  // Mapiranje između punih imena zemalja i kodova za CountrySelector
  const countryNameToCode = (countryName: string): string => {
    const countryMap: { [key: string]: string } = {
      Albanija: 'albania',
      'Bosna i Hercegovina': 'bosnia-herzegovina',
      Bugarska: 'bulgaria',
      'Crna Gora': 'montenegro',
      Grčka: 'greece',
      Hrvatska: 'croatia',
      Kosovo: 'kosovo',
      'Sjeverna Makedonija': 'north-macedonia',
      Rumunija: 'romania',
      Srbija: 'serbia',
      Slovenija: 'slovenia',
    };
    return countryMap[countryName] || '';
  };

  const countryCodeToName = (countryCode: string): string => {
    const codeMap: { [key: string]: string } = {
      albania: 'Albanija',
      'bosnia-herzegovina': 'Bosna i Hercegovina',
      bulgaria: 'Bugarska',
      montenegro: 'Crna Gora',
      greece: 'Grčka',
      croatia: 'Hrvatska',
      kosovo: 'Kosovo',
      'north-macedonia': 'Sjeverna Makedonija',
      romania: 'Rumunija',
      serbia: 'Srbija',
      slovenia: 'Slovenija',
    };
    return codeMap[countryCode] || countryCode;
  };

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<ProfileForm>({
    resolver: zodResolver(profileSchema),
  });

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  // Watch form values for preview
  const watchedValues = watch();

  // Create preview data for InfluencerCard
  const createPreviewData = (): InfluencerSearchResult => {
    const totalFollowers =
      (watchedValues.instagram_followers || 0) +
      (watchedValues.tiktok_followers || 0) +
      (watchedValues.youtube_subscribers || 0);

    const platforms = [];
    if (
      watchedValues.instagram_followers &&
      watchedValues.instagram_followers > 0
    ) {
      platforms.push({
        platform_id: 1,
        platform_name: 'Instagram',
        platform_icon: 'Instagram',
        handle: watchedValues.instagram_handle || '@username',
        followers_count: watchedValues.instagram_followers,
        is_verified: false,
      });
    }
    if (watchedValues.tiktok_followers && watchedValues.tiktok_followers > 0) {
      platforms.push({
        platform_id: 2,
        platform_name: 'TikTok',
        platform_icon: 'TikTok',
        handle: watchedValues.tiktok_handle || '@username',
        followers_count: watchedValues.tiktok_followers,
        is_verified: false,
      });
    }
    if (
      watchedValues.youtube_subscribers &&
      watchedValues.youtube_subscribers > 0
    ) {
      platforms.push({
        platform_id: 3,
        platform_name: 'YouTube',
        platform_icon: 'YouTube',
        handle: watchedValues.youtube_handle || '@username',
        followers_count: watchedValues.youtube_subscribers,
        is_verified: false,
      });
    }

    return {
      id: user?.id || 'preview',
      username: watchedValues.username || 'username',
      full_name: watchedValues.public_display_name || 'Vaše ime',
      avatar_url: profile?.avatar_url || '',
      navbar_avatar_url: profile?.navbar_avatar_url || '',
      card_avatar_url: profile?.card_avatar_url || '',
      profile_avatar_url: profile?.profile_avatar_url || '',
      preview_avatar_url: profile?.preview_avatar_url || '',
      bio: watchedValues.bio || 'Vaš bio...',
      location: watchedValues.location || 'Lokacija',
      gender: 'prefer_not_to_say',
      age: 0,
      subscription_type:
        (profile?.subscription_type as 'free' | 'premium') || 'free',
      custom_offers_enabled: customOffersEnabled,
      categories: [],
      platforms,
      pricing: [],
      min_price: 0,
      max_price: 0,
      total_followers: totalFollowers,
      relevance_score: 1.0,
      average_rating: 0,
      total_reviews: 0,
    };
  };

  const loadData = async () => {
    try {
      setLoading(true);
      const { data: profileData, error: profileError } = await getProfile(
        user!.id
      );
      if (profileError) {
        toast.error('Greška pri učitavanju profila');
        return;
      }

      // Load influencer data
      const { data: influencerData, error: influencerError } =
        await getInfluencer(user!.id);
      if (influencerError) {
        toast.error('Greška pri učitavanju influencer podataka');
        return;
      }

      // Load influencer platforms
      const { data: platformsData, error: platformsError } =
        await getInfluencerPlatforms(user!.id);

      // Extract platform data
      const instagramPlatform = platformsData?.find(p => p.platform_id === 1);
      const tiktokPlatform = platformsData?.find(p => p.platform_id === 2);
      const youtubePlatform = platformsData?.find(p => p.platform_id === 3);

      // Load influencer categories
      const { data: categoriesData, error: categoriesError } =
        await getInfluencerCategories(user!.id);
      if (!categoriesError && categoriesData) {
        setCategories(categoriesData);
      }

      // Load pricing packages
      loadPackages();

      // Set profile data for additional info section (combine profile and influencer data)
      setProfile({
        ...profileData,
        ...influencerData,
      });

      // Set custom offers enabled state from influencer data
      setCustomOffersEnabled(influencerData?.custom_offers_enabled || false);

      // Popuni formu sa postojećim podacima
      reset({
        username: profileData?.username || '',
        public_display_name: profileData?.public_display_name || '',
        bio: profileData?.bio || '',
        location: profileData?.city || '', // Use city instead of location
        country: countryNameToCode(profileData?.country || ''), // Convert country name to code
        instagram_handle: instagramPlatform?.handle || '',
        instagram_followers: instagramPlatform?.followers_count || 0,
        tiktok_handle: tiktokPlatform?.handle || '',
        tiktok_followers: tiktokPlatform?.followers_count || 0,
        youtube_handle: youtubePlatform?.handle || '',
        youtube_subscribers: youtubePlatform?.followers_count || 0,
      });
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Greška pri učitavanju podataka');
    } finally {
      setLoading(false);
    }
  };

  const loadPackages = async () => {
    try {
      setPackagesLoading(true);
      const { data: packagesData, error: packagesError } =
        await getInfluencerPackages(user!.id);

      if (packagesError) {
        console.error('Error loading packages:', packagesError);
        setPackages([]);
      } else {
        setPackages(packagesData || []);
      }
    } catch (error) {
      console.error('Error loading packages:', error);
      setPackages([]);
    } finally {
      setPackagesLoading(false);
    }
  };

  const handleCustomOffersToggle = async (enabled: boolean) => {
    if (enabled && profile?.subscription_type !== 'premium') {
      setShowUpgradeModal(true);
      return;
    }

    try {
      // Update in database
      const { error } = await supabase
        .from('influencers')
        .update({ custom_offers_enabled: enabled })
        .eq('id', user!.id);

      if (error) {
        console.error('Error updating custom offers setting:', error);
        toast.error('Greška pri čuvanju postavke');
        return;
      }

      setCustomOffersEnabled(enabled);
      toast.success(
        enabled
          ? 'Uspješno ste aktivirali primanje custom ponuda'
          : 'Uspješno ste deaktivirali primanje custom ponuda'
      );
    } catch (error) {
      console.error('Error updating custom offers setting:', error);
      toast.error('Greška pri čuvanju postavke');
    }
  };

  const onSubmit = async (data: ProfileForm) => {
    try {
      setSaving(true);

      // Update profile
      const { error: profileError } = await updateProfile(user!.id, {
        username: data.username,
        public_display_name: data.public_display_name || null,
        bio: data.bio || null,
        city: data.location || null, // Use city instead of location
        country: countryCodeToName(data.country || ''), // Convert country code to name
      });

      if (profileError) {
        toast.error('Greška pri ažuriranju profila');
        return;
      }

      // Update platform data using the new platform system
      const platformUpdates = [
        {
          platform_id: 1,
          handle: data.instagram_handle,
          followers_count: data.instagram_followers,
        },
        {
          platform_id: 2,
          handle: data.tiktok_handle,
          followers_count: data.tiktok_followers,
        },
        {
          platform_id: 3,
          handle: data.youtube_handle,
          followers_count: data.youtube_subscribers,
        },
      ];

      for (const platform of platformUpdates) {
        if (platform.handle) {
          // Upsert platform data
          const { error: platformError } = await supabase
            .from('influencer_platforms')
            .upsert({
              influencer_id: user!.id,
              platform_id: platform.platform_id,
              handle: platform.handle,
              followers_count: platform.followers_count || 0,
              is_active: true,
            });

          if (platformError) {
            console.error('Error updating platform:', platformError);
          }
        }
      }

      toast.success('Profil je uspješno ažuriran!');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Greška pri ažuriranju profila');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            Profil
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Upravljajte vašim profilom i informacijama
          </p>
        </div>

        {/* Responsive Grid Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Form Content - Left Side (Desktop) / Top (Mobile) */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Profile Picture */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <User className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Profilna slika
                    </h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                    Podesite svoju profilnu sliku.
                  </p>

                  <AvatarUpload
                    userId={user!.id}
                    currentAvatarUrl={profile?.avatar_url || undefined}
                    onUploadComplete={avatarUrls => {
                      toast.success('Profilna slika je uspješno upload-ovana!');
                      // Refresh profile data to show new avatar
                      loadData();
                    }}
                    onUploadError={error => {
                      toast.error(`Greška pri upload-u: ${error}`);
                    }}
                  />
                </div>
              </div>

              {/* Basic Information */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <User className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Osnovne informacije
                    </h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                    Ažurirajte vaše osnovne informacije
                  </p>

                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label
                          htmlFor="username"
                          className="text-gray-900 dark:text-gray-100"
                        >
                          Username
                        </Label>
                        <Input
                          id="username"
                          {...register('username')}
                          placeholder="Unesite username"
                          disabled
                          className="bg-gray-100/60 dark:bg-gray-700/40 border-purple-200/50 cursor-not-allowed"
                        />
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          Username se ne može mijenjati nakon registracije
                        </p>
                        {errors.username && (
                          <p className="text-sm text-red-600">
                            {errors.username.message}
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label
                          htmlFor="public_display_name"
                          className="text-gray-900 dark:text-gray-100"
                        >
                          Javno ime i prezime
                        </Label>
                        <Input
                          id="public_display_name"
                          {...register('public_display_name')}
                          placeholder="Unesite ime i prezime koje će biti vidljivo drugima"
                          className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                        />
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          Ovo ime će biti vidljivo drugim korisnicima na vašem
                          javnom profilu. Ostavite prazno ako ne želite da se
                          prikazuje.
                        </p>
                        {errors.public_display_name && (
                          <p className="text-sm text-red-600">
                            {errors.public_display_name.message}
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label
                          htmlFor="location"
                          className="text-gray-900 dark:text-gray-100"
                        >
                          Grad
                        </Label>
                        <Input
                          id="location"
                          {...register('location')}
                          placeholder="Unesite grad/lokaciju"
                          className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                        />
                        {errors.location && (
                          <p className="text-sm text-red-600">
                            {errors.location.message}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label
                          htmlFor="country"
                          className="text-gray-900 dark:text-gray-100"
                        >
                          Država
                        </Label>
                        <CountrySelector
                          value={watch('country')}
                          onValueChange={value => setValue('country', value)}
                          placeholder="Izaberite državu..."
                        />
                        {errors.country && (
                          <p className="text-sm text-red-600">
                            {errors.country.message}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="bio"
                        className="text-gray-900 dark:text-gray-100"
                      >
                        Bio
                      </Label>
                      <Textarea
                        id="bio"
                        {...register('bio')}
                        placeholder="Opišite sebe..."
                        rows={4}
                        className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                      />
                      {errors.bio && (
                        <p className="text-sm text-red-600">
                          {errors.bio.message}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Social Media */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Globe className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Društvene mreže
                    </h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                    Dodajte vaše profile na društvenim mrežama
                  </p>

                  <div className="space-y-4">
                    {/* Instagram */}
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <PlatformIconSimple
                          platform="Instagram"
                          size="md"
                        />
                        <Label className="text-base font-medium">
                          Instagram
                        </Label>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ml-7">
                        <div className="space-y-2">
                          <Label
                            htmlFor="instagram_handle"
                            className="text-gray-900 dark:text-gray-100"
                          >
                            Handle
                          </Label>
                          <Input
                            id="instagram_handle"
                            {...register('instagram_handle')}
                            placeholder="@username"
                            className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label
                            htmlFor="instagram_followers"
                            className="text-gray-900 dark:text-gray-100"
                          >
                            Pratilaca
                          </Label>
                          <Input
                            id="instagram_followers"
                            type="number"
                            {...register('instagram_followers', {
                              valueAsNumber: true,
                            })}
                            placeholder="0"
                            className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                          />
                        </div>
                      </div>
                    </div>

                    {/* TikTok */}
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <PlatformIconSimple
                          platform="TikTok"
                          size="md"
                        />
                        <Label className="text-base font-medium">TikTok</Label>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ml-7">
                        <div className="space-y-2">
                          <Label
                            htmlFor="tiktok_handle"
                            className="text-gray-900 dark:text-gray-100"
                          >
                            Handle
                          </Label>
                          <Input
                            id="tiktok_handle"
                            {...register('tiktok_handle')}
                            placeholder="@username"
                            className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label
                            htmlFor="tiktok_followers"
                            className="text-gray-900 dark:text-gray-100"
                          >
                            Pratilaca
                          </Label>
                          <Input
                            id="tiktok_followers"
                            type="number"
                            {...register('tiktok_followers', {
                              valueAsNumber: true,
                            })}
                            placeholder="0"
                            className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                          />
                        </div>
                      </div>
                    </div>

                    {/* YouTube */}
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <PlatformIconSimple
                          platform="YouTube"
                          size="md"
                        />
                        <Label className="text-base font-medium">YouTube</Label>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ml-7">
                        <div className="space-y-2">
                          <Label
                            htmlFor="youtube_handle"
                            className="text-gray-900 dark:text-gray-100"
                          >
                            Handle
                          </Label>
                          <Input
                            id="youtube_handle"
                            {...register('youtube_handle')}
                            placeholder="@username"
                            className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label
                            htmlFor="youtube_subscribers"
                            className="text-gray-900 dark:text-gray-100"
                          >
                            Pretplatnika
                          </Label>
                          <Input
                            id="youtube_subscribers"
                            type="number"
                            {...register('youtube_subscribers', {
                              valueAsNumber: true,
                            })}
                            placeholder="0"
                            className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Pricing Packages Link */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Package className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Paketi i cijene
                    </h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                    Upravljajte vašim paketima i cijenama
                  </p>
                  {packagesLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      <span className="text-gray-600 dark:text-gray-400">
                        Učitavanje paketa...
                      </span>
                    </div>
                  ) : packages.length > 0 ? (
                    <div className="space-y-4">
                      <div className="grid gap-3">
                        {packages.map(pkg => (
                          <div
                            key={pkg.id}
                            className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/30 transition-colors"
                          >
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
                                <PlatformIconSimple
                                  platform={pkg.platform_name}
                                  size="md"
                                />
                              </div>
                              <div>
                                <p className="font-medium text-sm">
                                  {pkg.auto_generated_name}
                                </p>
                                <p className="text-xs text-gray-600 dark:text-gray-400">
                                  {pkg.platform_name} • {pkg.content_type_name}
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-semibold text-sm">
                                {pkg.price} {pkg.currency}
                              </p>
                              <p className="text-xs text-gray-600 dark:text-gray-400">
                                {pkg.quantity > 1 ? `${pkg.quantity}x` : ''}
                                {pkg.video_duration &&
                                  ` • ${pkg.video_duration}`}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="flex justify-end pt-2">
                        <Link href="/dashboard/influencer/pricing">
                          <button className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white/70 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800/70 border border-gray-200/50 dark:border-gray-600/50 rounded-lg transition-all duration-200">
                            <Package className="h-4 w-4" />
                            Upravljaj paketima
                          </button>
                        </Link>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Package className="h-12 w-12 mx-auto text-gray-600 dark:text-gray-400 mb-4" />
                      <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-gray-100">
                        Nema paketa
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        Još niste kreirali nijedan paket. Kreirajte pakete da
                        biste mogli primati ponude od brendova.
                      </p>
                      <Link href="/dashboard/influencer/pricing">
                        <button className="flex items-center gap-2 px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 rounded-lg transition-all duration-200 hover:shadow-lg">
                          <Plus className="h-4 w-4" />
                          Kreiraj prvi paket
                        </button>
                      </Link>
                    </div>
                  )}
                  
                  {/* Custom Offers Toggle */}
                  <div className="mt-6 pt-6 border-t border-purple-200/50 dark:border-purple-700/30">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <h4 className="text-base font-medium text-gray-900 dark:text-gray-100">
                            Primanje custom ponuda
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Omogućite brendovima da vam pošalju personalizovane ponude
                          </p>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Isključeno</span>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              checked={customOffersEnabled}
                              onChange={(e) => handleCustomOffersToggle(e.target.checked)}
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 opacity-50 peer-checked:opacity-100"></div>
                          </label>
                          <span className="text-sm text-gray-600 dark:text-gray-400">Uključeno</span>
                        </div>
                      </div>
                      {profile?.subscription_type !== 'premium' && (
                        <div className="flex items-center gap-2 p-3 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 border border-amber-200 dark:border-amber-700 rounded-lg">
                          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-amber-400 to-yellow-500 flex items-center justify-center flex-shrink-0">
                            <Crown className="w-4 h-4 text-white" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-amber-800 dark:text-amber-200">
                              Premium funkcija
                            </p>
                            <p className="text-xs text-amber-700 dark:text-amber-300">
                              Potreban je Premium plan za primanje custom ponuda
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Profile Information */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <User className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Dodatne informacije
                    </h3>
                  </div>

                  <div className="space-y-4">
                    {/* Age */}
                    {profile?.age && (
                      <div className="space-y-2">
                        <Label className="text-gray-900 dark:text-gray-100">
                          Godine
                        </Label>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {profile.age} godina
                        </div>
                      </div>
                    )}

                    {/* Gender */}
                    {profile?.gender && (
                      <div className="space-y-2">
                        <Label className="text-gray-900 dark:text-gray-100">
                          Pol
                        </Label>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {profile.gender === 'male'
                            ? 'Muško'
                            : profile.gender === 'female'
                              ? 'Žensko'
                              : 'Ostalo'}
                        </div>
                      </div>
                    )}

                    {/* Categories */}
                    {categories && categories.length > 0 && (
                      <div className="space-y-2">
                        <Label className="text-gray-900 dark:text-gray-100">
                          Kategorije
                        </Label>
                        <div className="flex flex-wrap gap-2">
                          {categories.map((category, index) => (
                            <Badge key={index} variant="secondary">
                              {category.categories?.name ||
                                category.category_name}
                            </Badge>
                          ))}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          Za izmenu kategorija i pola, kontaktirajte podršku.
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={saving}
                  className="flex items-center gap-2 px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 rounded-lg transition-all duration-200 hover:shadow-lg disabled:opacity-50"
                >
                  {saving ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Čuvanje...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4" />
                      Sačuvaj promjene
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>

          {/* Preview Sidebar - Right Side (Desktop) / Bottom (Mobile) */}
          <div className="lg:col-span-1">
            <div className="lg:sticky lg:top-6">
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <Eye className="h-5 w-5 text-purple-500" />
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        Kako vas vide brendovi
                      </h3>
                    </div>
                    <Badge variant="secondary">Preview</Badge>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                    Ovako će vaš profil izgledati u marketplace-u
                  </p>

                  <div className="max-w-sm mx-auto lg:max-w-none">
                    <InfluencerCard
                      influencer={createPreviewData()}
                      disableClick={true}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Upgrade Required Modal */}
      <UpgradeRequiredModal
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        feature="custom_offers_receiving"
      />

      {/* Debug Component */}
      <InfluencerSubscriptionDebugger />
    </DashboardLayout>
  );
}
