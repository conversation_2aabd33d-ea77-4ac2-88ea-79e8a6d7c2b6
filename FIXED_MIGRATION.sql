-- FIXED SQL MIGRATION for Featured Campaign Payments
-- Run this in Supabase Dashboard → SQL Editor

-- Add campaign_id column for featured campaign payments
ALTER TABLE payments 
ADD COLUMN IF NOT EXISTS campaign_id UUID REFERENCES campaigns(id);

-- Add featured_until column to campaigns table
ALTER TABLE campaigns 
ADD COLUMN IF NOT EXISTS featured_until TIMESTAMP WITH TIME ZONE;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_payments_campaign_id ON payments(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaigns_featured_until ON campaigns(featured_until);

-- Update RLS policies to include campaign_id
DROP POLICY IF EXISTS "payments_select_policy" ON payments;
CREATE POLICY "payments_select_policy" ON payments FOR SELECT USING (
  -- Business can see payments for their campaigns/applications/offers
  EXISTS (
    SELECT 1 FROM campaigns c WHERE c.id = payments.campaign_id AND c.business_id = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM campaign_applications ca 
    JOIN campaigns c ON c.id = ca.campaign_id 
    WHERE ca.id = payments.campaign_application_id AND c.business_id = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM direct_offers d WHERE d.id = payments.direct_offer_id AND d.business_id = auth.uid()
  ) OR
  -- Influencer can see payments for accepted applications/offers
  EXISTS (
    SELECT 1 FROM campaign_applications ca WHERE ca.id = payments.campaign_application_id AND ca.influencer_id = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM direct_offers d WHERE d.id = payments.direct_offer_id AND d.influencer_id = auth.uid()
  )
);

-- Update INSERT policy for payments
DROP POLICY IF EXISTS "payments_insert_policy" ON payments;
CREATE POLICY "payments_insert_policy" ON payments FOR INSERT WITH CHECK (false);

-- Update UPDATE policy for payments  
DROP POLICY IF EXISTS "payments_update_policy" ON payments;
CREATE POLICY "payments_update_policy" ON payments FOR UPDATE USING (false);
