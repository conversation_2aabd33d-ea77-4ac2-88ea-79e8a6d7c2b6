import { notFound } from 'next/navigation';
import { Metada<PERSON> } from 'next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  MapPin,
  Users,
  Star,
  Verified,
  Mail,
  MessageCircle,
  Calendar,
  Globe,
} from 'lucide-react';
import { getPublicInfluencerProfile } from '@/lib/profiles';
import { getDisplayName } from '@/lib/utils';
import Link from 'next/link';
import { InfluencerProfileWithAccessControl } from './InfluencerProfileWithAccessControl';

interface PageProps {
  params: {
    username: string;
  };
}

// Metadata za SEO
export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const { data: profile } = await getPublicInfluencerProfile(
    resolvedParams.username
  );

  if (!profile) {
    return {
      title: 'Influencer nije pronađen - InfluConnect',
      description: 'Traženi influencer profil nije pronađen.',
    };
  }

  const displayName = getDisplayName(profile);
  const metaTitle =
    displayName !== 'Ime i prezime skriveno'
      ? `${displayName} (@${profile.username}) - InfluConnect`
      : `@${profile.username} - InfluConnect`;

  return {
    title: metaTitle,
    description:
      profile.bio || `Pogledajte profil influencera na InfluConnect platformi.`,
    openGraph: {
      title: metaTitle,
      description: profile.bio || '',
      images: profile.avatar_url ? [profile.avatar_url] : [],
    },
  };
}

export default async function InfluencerProfilePage({ params }: PageProps) {
  const resolvedParams = await params;

  // Dohvati profil podatke
  const { data: profile, error } = await getPublicInfluencerProfile(
    resolvedParams.username
  );

  if (error || !profile) {
    notFound();
  }

  // Proslijedi podatke client komponenti koja će provjeriti access control
  return (
    <InfluencerProfileWithAccessControl
      profile={profile}
      targetUsername={resolvedParams.username}
    />
  );
}
