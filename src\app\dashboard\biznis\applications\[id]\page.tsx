'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AcceptButton } from '@/components/ui/accept-button';
import { RejectButton } from '@/components/ui/reject-button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  ArrowLeft,
  CheckCircle,
  XCircle,
  User,
  Calendar,
  Euro,
  ExternalLink,
  MessageSquare,
  MessageCircle,
  Star,
  Clock,
  Tag,
  Users,
  Building2,
  FileText,
  CreditCard,
} from 'lucide-react';
import Link from 'next/link';
import {
  getCampaignApplication,
  updateApplicationStatus,
  getApplicationPaymentInfo,
} from '@/lib/campaigns';
import { ChatEnableButton } from '@/components/chat/ChatEnableButton';
import { canViewApplicationDetails } from '@/lib/subscriptions';
import { PaywallOverlay } from '@/components/premium/PaywallOverlay';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import {
  getJobCompletionByCampaignApplication,
  type JobCompletion,
} from '@/lib/job-completions';
import { ApproveJobModal } from '@/components/job-completion/ApproveJobModal';
import { RejectJobModal } from '@/components/job-completion/RejectJobModal';
import { ApplicationPaymentButton } from '@/components/campaigns/ApplicationPaymentButton';
import { formatDate } from '@/lib/date-utils';
import { getDisplayName, getInitials } from '@/lib/utils';
import { BackButton } from '@/components/ui/back-button';
import { toast } from 'sonner';

interface ApplicationDetails {
  id: string;
  campaign_id: string;
  influencer_id: string;
  status: 'pending' | 'accepted' | 'rejected';
  proposed_rate: number;
  proposal_text: string;
  delivery_timeframe: string;
  portfolio_links: string[];
  experience_relevant: string;
  audience_insights: string;
  applied_at: string;
  campaign: {
    id: string;
    title: string;
    description: string;
    budget: number;
    requirements: string;
    deliverables: string;
    business_id: string;
  };
  influencer: {
    id: string;
    profile: {
      id: string;
      full_name: string;
      username: string;
      avatar_url: string;
      bio: string;
      city: string;
      country: string;
      age: number;
      gender: string;
    };
    categories: string[];
    platforms: Array<{
      platform_name: string;
      handle: string;
      followers_count: number;
    }>;
  };
}

export default function ApplicationDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [application, setApplication] = useState<ApplicationDetails | null>(
    null
  );
  const [paymentInfo, setPaymentInfo] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectionForm, setShowRejectionForm] = useState(false);
  const [jobCompletion, setJobCompletion] = useState<JobCompletion | null>(
    null
  );
  const [isLoadingJobCompletion, setIsLoadingJobCompletion] = useState(false);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [userSubscriptionType, setUserSubscriptionType] = useState<
    'free' | 'premium'
  >('free');

  useEffect(() => {
    if (!user || !params.id) return;

    const loadApplicationAndSubscription = async () => {
      try {
        // Load user subscription type first
        const { supabase } = await import('@/lib/supabase');
        const { data: business } = await supabase
          .from('businesses')
          .select('subscription_type')
          .eq('id', user.id)
          .single();

        if (business) {
          setUserSubscriptionType(
            (business.subscription_type as 'free' | 'premium') || 'free'
          );
        }

        // Load application (server-side protection already handles subscription checks)
        const { data, error } = await getCampaignApplication(
          params.id as string
        );
        if (error) {
          console.error('Error loading application:', error);
          return;
        }
        setApplication(data);

        // Load payment info if application is accepted
        if (data && data.status === 'accepted') {
          console.log('Loading payment info for application:', data.id);
          console.log('Application data object:', data);
          console.log('Application ID type:', typeof data.id);
          const paymentData = await getApplicationPaymentInfo(data.id);
          console.log('Payment data loaded:', paymentData);
          setPaymentInfo(paymentData);
          loadJobCompletion(params.id as string);
        }
      } catch (error) {
        console.error('Error loading application:', error);
      } finally {
        setLoading(false);
      }
    };

    loadApplicationAndSubscription();
  }, [user, params.id]);

  const loadJobCompletion = async (applicationId: string) => {
    setIsLoadingJobCompletion(true);
    try {
      const { data, error } =
        await getJobCompletionByCampaignApplication(applicationId);
      if (error) {
        console.error('Error loading job completion:', error);
        return;
      }
      setJobCompletion(data);
    } catch (error) {
      console.error('Error loading job completion:', error);
    } finally {
      setIsLoadingJobCompletion(false);
    }
  };

  const handleStatusUpdate = async (
    status: 'accepted' | 'rejected',
    reason?: string
  ) => {
    if (!application) return;

    setUpdating(true);
    try {
      const { error } = await updateApplicationStatus(
        application.id,
        status,
        reason
      );
      if (error) {
        console.error('Error updating application:', error);
        return;
      }

      setApplication(prev => (prev ? { ...prev, status } : null));
      setShowRejectionForm(false);
      setRejectionReason('');
    } catch (error) {
      console.error('Error updating application:', error);
    } finally {
      setUpdating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 border-yellow-200';
      case 'accepted':
        return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border-red-200';
      default:
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbačeno';
      default:
        return status;
    }
  };

  const getGenderLabel = (gender: string) => {
    const genderMap = {
      male: 'Muško',
      female: 'Žensko',
      other: 'Ostalo',
      prefer_not_to_say: 'Ne želim da kažem',
    };
    return genderMap[gender as keyof typeof genderMap] || gender;
  };

  const getJobStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary">Čeka se početak rada</Badge>;
      case 'submitted':
        return <Badge variant="outline">Poslano na pregled</Badge>;
      case 'approved':
        return <Badge variant="default">Odobreno</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Odbačeno</Badge>;
      case 'completed':
        return <Badge variant="default">Završeno</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const handlePaymentSuccess = async () => {
    // Reload application to get updated payment status
    const { data: updatedApp } = await getCampaignApplication(
      application.id
    );
    if (updatedApp) {
      setApplication(updatedApp);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!application) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold mb-4 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Aplikacija nije pronađena
            </h2>
            <Link href="/dashboard/biznis/applications">
              <BackButton>Nazad na aplikacije</BackButton>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <div className="hidden md:block">
            <Link href="/dashboard/biznis/applications">
              <BackButton />
            </Link>
          </div>
          <div className="flex-1">
            <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Detalji aplikacije
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
              Pregled aplikacije za kampanju "{application.campaign.title}"
            </p>
          </div>
          <Badge
            variant="outline"
            className={`${getStatusColor(application.status)} font-medium`}
          >
            <span>{getStatusText(application.status)}</span>
          </Badge>
        </div>

        <PaywallOverlay
          isVisible={!canViewApplicationDetails(userSubscriptionType)}
          title="Premium plan potreban za detalje"
          description="Da biste videli detaljne informacije o aplikaciji potreban je Premium plan"
          ctaText="Nadogradite na Premium"
          benefits={[
            'Detaljan pregled svih aplikacija',
            'Potpune informacije o influencerima',
            'Kontakt podaci i portfolio linkovi',
            'Uvid u publiku i iskustvo',
            'Chat komunikacija sa influencerima',
            'Napredni filteri i pretragu',
          ]}
        >
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Influencer Profile */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6 space-y-6">
                  <div className="flex items-center gap-2 mb-4">
                    <User className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Profil influencera
                    </h3>
                  </div>

                  {/* Header Section - Avatar, Name, Bio */}
                  <div className="flex flex-col sm:flex-row gap-6">
                    <div className="flex-shrink-0">
                      <Avatar className="h-20 w-20 mx-auto sm:mx-0 border-2 border-white/50">
                        <AvatarImage
                          src={application.influencer.profile.avatar_url}
                        />
                        <AvatarFallback className="text-lg bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700">
                          {getInitials(
                            getDisplayName(application.influencer.profile)
                          )}
                        </AvatarFallback>
                      </Avatar>
                    </div>

                    <div className="flex-1 text-center sm:text-left">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-3">
                        <h4 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                          {getDisplayName(application.influencer.profile)}
                        </h4>
                        <Badge
                          variant="secondary"
                          className="w-fit mx-auto sm:mx-0 bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200"
                        >
                          @{application.influencer.profile.username}
                        </Badge>
                      </div>

                      {application.influencer.profile.bio && (
                        <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                          {application.influencer.profile.bio}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Profile Link Button */}
                  <Link href={`/influencer/${application.influencer.profile.username}`}>
                    <button className="w-full flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium bg-white/70 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800/70 border border-gray-200/50 dark:border-gray-700/50 rounded-lg transition-all duration-200 hover:shadow-md">
                      <User className="h-4 w-4" />
                      Pogledaj profil
                    </button>
                  </Link>

                  {/* Basic Info Grid */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 p-4 bg-white/60 dark:bg-gray-800/40 rounded-lg border border-purple-100/50 dark:border-purple-800/30">
                    {application.influencer.profile.age && (
                      <div className="text-center">
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                          Godine
                        </p>
                        <p className="font-semibold text-gray-900 dark:text-gray-100">
                          {application.influencer.profile.age}
                        </p>
                      </div>
                    )}
                    {application.influencer.profile.gender && (
                      <div className="text-center">
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                          Spol
                        </p>
                        <p className="font-semibold text-gray-900 dark:text-gray-100">
                          {getGenderLabel(
                            application.influencer.profile.gender
                          )}
                        </p>
                      </div>
                    )}
                    {application.influencer.profile.city && (
                      <div className="text-center">
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                          Lokacija
                        </p>
                        <p className="font-semibold text-gray-900 dark:text-gray-100">
                          {application.influencer.profile.city}
                          {application.influencer.profile.country &&
                            `, ${application.influencer.profile.country}`}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Categories */}
              {application.influencer.categories &&
                application.influencer.categories.length > 0 && (
                  <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                    <div className="relative p-6">
                      <div className="flex items-center gap-2 mb-4">
                        <Tag className="h-5 w-5 text-purple-500" />
                        <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                          Kategorije
                        </h3>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {application.influencer.categories.map(
                          (category, index) => (
                            <Badge
                              key={index}
                              variant="secondary"
                              className="text-xs bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200"
                            >
                              {category}
                            </Badge>
                          )
                        )}
                      </div>
                    </div>
                  </div>
                )}

              {/* Social Media Platforms */}
              {application.influencer.platforms &&
                application.influencer.platforms.length > 0 && (
                  <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                    <div className="relative p-6">
                      <div className="flex items-center gap-2 mb-4">
                        <Users className="h-5 w-5 text-purple-500" />
                        <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                          Društvene mreže
                        </h3>
                      </div>
                      <div className="space-y-3">
                        {application.influencer.platforms.map(
                          (platform, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between p-3 bg-white/60 dark:bg-gray-800/40 rounded-lg border border-purple-100/50 dark:border-purple-800/30"
                            >
                              <div className="flex items-center gap-3">
                                <PlatformIconSimple
                                  platform={platform.platform_name}
                                  size="md"
                                />
                                <div>
                                  <p className="font-medium text-sm text-gray-900 dark:text-gray-100">
                                    {platform.platform_name}
                                  </p>
                                  {platform.handle && (
                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                      @{platform.handle}
                                    </p>
                                  )}
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="font-semibold text-sm text-gray-900 dark:text-gray-100">
                                  {platform.followers_count?.toLocaleString()}
                                </p>
                                <p className="text-xs text-gray-600 dark:text-gray-400">
                                  pratilaca
                                </p>
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  </div>
                )}

              {/* Application Overview */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6 space-y-4">
                  <div className="flex items-center gap-2 mb-4">
                    <FileText className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Pregled aplikacije
                    </h3>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                      <div className="flex items-center gap-2">
                        <Euro className="h-4 w-4 text-purple-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Predložena cijena:
                        </span>
                      </div>
                      <p className="font-semibold text-gray-900 dark:text-gray-100 mt-1">
                        {application.proposed_rate} €
                      </p>
                    </div>

                    <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-purple-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Vrijeme isporuke:
                        </span>
                      </div>
                      <p className="font-semibold text-gray-900 dark:text-gray-100 mt-1">
                        {application.delivery_timeframe}
                      </p>
                    </div>

                    <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-purple-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Datum aplikacije:
                        </span>
                      </div>
                      <p className="font-semibold text-gray-900 dark:text-gray-100 mt-1">
                        {formatDate(application.applied_at)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Proposal Message */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <MessageSquare className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Poruka aplikacije
                    </h3>
                  </div>
                  <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-4 border border-purple-100/50 dark:border-purple-800/30">
                    <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap leading-relaxed">
                      {application.proposal_text || 'Nema poruke'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Experience */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Star className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Relevantno iskustvo
                    </h3>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap leading-relaxed">
                    {application.experience_relevant ||
                      'Nema informacija o iskustvu'}
                  </p>
                </div>
              </div>

              {/* Audience Insights */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Users className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Insights o publici
                    </h3>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap leading-relaxed">
                    {application.audience_insights}
                  </p>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Campaign Info */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Building2 className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Kampanja
                    </h3>
                  </div>
                  <h4 className="font-semibold text-lg text-gray-900 dark:text-gray-100 mb-2">
                    {application.campaign.title}
                  </h4>
                  <p className="text-gray-700 dark:text-gray-300 mb-4 leading-relaxed">
                    {application.campaign.description}
                  </p>

                  <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                    <div className="flex items-center gap-2">
                      <Euro className="w-4 h-4 text-purple-500" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Budget:
                      </span>
                      <span className="font-semibold text-gray-900 dark:text-gray-100">
                        {application.campaign.budget} €
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Actions */}
              {application.status === 'pending' && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                  <div className="relative p-6 space-y-3">
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                      Akcije
                    </h3>
                    <AcceptButton
                      onClick={() => handleStatusUpdate('accepted')}
                      disabled={updating}
                      className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium"
                    >
                      <CheckCircle className="h-4 w-4" />
                      Prihvati aplikaciju
                    </AcceptButton>

                    <RejectButton
                      onClick={() => setShowRejectionForm(true)}
                      disabled={updating}
                      className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium"
                      variant="secondary"
                    >
                      <XCircle className="h-4 w-4" />
                      Odbaci aplikaciju
                    </RejectButton>

                    {showRejectionForm && (
                      <div className="space-y-3 pt-4 border-t border-purple-200/50 dark:border-purple-800/30">
                        <Label
                          htmlFor="rejection-reason"
                          className="text-gray-900 dark:text-gray-100"
                        >
                          Razlog odbacivanja (opcionalno)
                        </Label>
                        <Textarea
                          id="rejection-reason"
                          value={rejectionReason}
                          onChange={e => setRejectionReason(e.target.value)}
                          placeholder="Objasnite zašto odbacujete ovu aplikaciju..."
                          rows={3}
                          className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                        />
                        <div className="flex space-x-2">
                          <RejectButton
                            onClick={() =>
                              handleStatusUpdate('rejected', rejectionReason)
                            }
                            disabled={updating}
                            className="flex-1 px-3 py-2 text-sm"
                          >
                            Potvrdi odbacivanje
                          </RejectButton>
                          <button
                            onClick={() => {
                              setShowRejectionForm(false);
                              setRejectionReason('');
                            }}
                            className="flex-1 px-3 py-2 text-sm font-medium bg-white/70 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800/70 border border-gray-200/50 dark:border-gray-700/50 rounded-lg transition-all duration-200 hover:shadow-md"
                          >
                            Otkaži
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Payment Section */}
              {application.status === 'accepted' &&
                !paymentInfo && (
                  <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-amber-50/80 via-orange-50/60 to-amber-100/80 dark:from-amber-950/20 dark:via-orange-950/10 dark:to-amber-900/30 border border-amber-200/50 dark:border-amber-800/30 backdrop-blur-sm shadow-lg">
                    <div className="absolute inset-0 bg-gradient-to-br from-amber-100/30 via-orange-100/20 to-amber-200/40 dark:from-amber-900/10 dark:via-orange-900/5 dark:to-amber-800/20 opacity-60" />
                    <div className="relative p-6">
                      <div className="flex items-center gap-2 mb-4">
                        <CreditCard className="h-5 w-5 text-amber-600" />
                        <h3 className="text-xl font-semibold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
                          Plaćanje potrebno
                        </h3>
                      </div>

                      <div className="space-y-4">
                        <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-4 border border-amber-100/50 dark:border-amber-800/30">
                          <p className="text-gray-700 dark:text-gray-300 mb-3">
                            Influencer je prihvatili u kampanju! Da biste počeli
                            komunikaciju i rad na projektu, potrebno je da
                            izvršite plaćanje.
                          </p>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">
                              Predložena cena:
                            </span>
                            <span className="font-semibold text-gray-900 dark:text-gray-100">
                              {application.proposed_rate.toLocaleString()} €
                            </span>
                          </div>
                        </div>

                        <ApplicationPaymentButton
                          applicationId={application.id}
                          proposedRate={application.proposed_rate}
                          onPaymentSuccess={handlePaymentSuccess}
                          className="w-full bg-gradient-to-r from-amber-500 via-orange-500 to-amber-600 hover:from-amber-600 hover:via-orange-600 hover:to-amber-700 text-white"
                        >
                          <CreditCard className="h-4 w-4 mr-2" />
                          Plati sada (
                          {application.proposed_rate.toLocaleString()} €)
                        </ApplicationPaymentButton>
                      </div>
                    </div>
                  </div>
                )}

              {/* Payment Confirmation */}
              {application.status === 'accepted' &&
                paymentInfo && (
                  <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-green-50/80 via-emerald-50/60 to-green-100/80 dark:from-green-950/20 dark:via-emerald-950/10 dark:to-green-900/30 border border-green-200/50 dark:border-green-800/30 backdrop-blur-sm shadow-lg">
                    <div className="absolute inset-0 bg-gradient-to-br from-green-100/30 via-emerald-100/20 to-green-200/40 dark:from-green-900/10 dark:via-emerald-900/5 dark:to-green-800/20 opacity-60" />
                    <div className="relative p-6">
                      <div className="flex items-center gap-2 mb-4">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <h3 className="text-xl font-semibold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                          Plaćanje završeno
                        </h3>
                      </div>

                      <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-4 border border-green-100/50 dark:border-green-800/30">
                        <p className="text-green-700 dark:text-green-300 mb-3">
                          ✅ Uspešno ste platili aplikaciju. Sada možete
                          komunicirati sa influencerom i početi rad na
                          kampaanji.
                        </p>
                        {paymentInfo && (
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-green-600 dark:text-green-400">
                              Plaćeno:
                            </span>
                            <span className="font-semibold text-green-900 dark:text-green-100">
                              {paymentInfo.total_paid?.toLocaleString()}{' '}
                              €
                              <span className="text-xs text-green-600 dark:text-green-400 ml-1">
                                (uključujući {Math.round((paymentInfo.platform_fee / paymentInfo.payment_amount) * 100)}% proviziju)
                              </span>
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

              {/* Chat Enable Button */}
              {application.status === 'accepted' &&
                paymentInfo && (
                  <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                    <div className="relative p-6">
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                        Komunikacija
                      </h3>
                      <Button
                        onClick={() => {
                          window.location.href = `/dashboard/chat?application=${application.id}`;
                        }}
                        className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0"
                      >
                        <MessageCircle className="h-4 w-4 mr-2" />
                        Otvori chat
                      </Button>
                    </div>
                  </div>
                )}

              {/* Job Completion */}
              {application.status === 'accepted' &&
                paymentInfo && (
                  <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                    <div className="relative p-6">
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                        Status rada
                      </h3>
                      {isLoadingJobCompletion ? (
                        <div className="animate-pulse space-y-2">
                          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                        </div>
                      ) : jobCompletion ? (
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              Status:
                            </span>
                            {getJobStatusBadge(
                              jobCompletion.status || 'pending'
                            )}
                          </div>

                          {jobCompletion.submitted_at && (
                            <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                Poslano:
                              </span>
                              <p className="text-sm text-gray-700 dark:text-gray-300 mt-1">
                                {formatDate(jobCompletion.submitted_at)}
                              </p>
                            </div>
                          )}

                          {jobCompletion.submission_notes && (
                            <div className="space-y-4">
                              {(() => {
                                try {
                                  const submissionData = JSON.parse(
                                    jobCompletion.submission_notes
                                  );
                                  return (
                                    <>
                                      {/* Post Links */}
                                      {submissionData.post_links &&
                                        submissionData.post_links.length >
                                          0 && (
                                          <div className="space-y-3">
                                            <div className="flex items-center gap-2">
                                              <ExternalLink className="h-4 w-4 text-blue-600" />
                                              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                Objavljeni postovi
                                              </span>
                                            </div>
                                            <div className="space-y-2">
                                              {submissionData.post_links.map(
                                                (
                                                  link: string,
                                                  index: number
                                                ) => (
                                                  <div
                                                    key={index}
                                                    className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 p-3 rounded-lg border border-blue-200/50 dark:border-blue-800/30"
                                                  >
                                                    <a
                                                      href={link}
                                                      target="_blank"
                                                      rel="noopener noreferrer"
                                                      className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium flex items-center gap-2 hover:underline"
                                                    >
                                                      <ExternalLink className="h-3 w-3" />
                                                      {link}
                                                    </a>
                                                  </div>
                                                )
                                              )}
                                            </div>
                                          </div>
                                        )}

                                      {/* Message */}
                                      {submissionData.message && (
                                        <div className="space-y-3">
                                          <div className="flex items-center gap-2">
                                            <MessageCircle className="h-4 w-4 text-green-600" />
                                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                              Poruka influencera
                                            </span>
                                          </div>
                                          <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 p-3 rounded-lg border border-green-200/50 dark:border-green-800/30">
                                            <p className="text-sm text-gray-700 dark:text-gray-300">
                                              {submissionData.message}
                                            </p>
                                          </div>
                                        </div>
                                      )}
                                    </>
                                  );
                                } catch {
                                  // Fallback for old format
                                  return (
                                    <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        Napomene influencera:
                                      </span>
                                      <p className="text-sm text-gray-700 dark:text-gray-300 mt-1 leading-relaxed">
                                        {jobCompletion.submission_notes}
                                      </p>
                                    </div>
                                  );
                                }
                              })()}
                            </div>
                          )}

                          {jobCompletion.status === 'submitted' && (
                            <div className="flex space-x-2">
                              <button
                                onClick={() => setShowApproveModal(true)}
                                className="flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-green-400 via-emerald-400 to-green-500 hover:from-green-500 hover:via-emerald-500 hover:to-green-600 rounded-lg transition-all duration-200 hover:shadow-lg"
                              >
                                <CheckCircle className="h-4 w-4" />
                                Odobri
                              </button>
                              <button
                                onClick={() => setShowRejectModal(true)}
                                className="flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-red-400 via-pink-400 to-red-500 hover:from-red-500 hover:via-pink-500 hover:to-red-600 rounded-lg transition-all duration-200 hover:shadow-lg"
                              >
                                <XCircle className="h-4 w-4" />
                                Odbaci
                              </button>
                            </div>
                          )}

                          {jobCompletion.business_notes && (
                            <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                Vaše napomene:
                              </span>
                              <p className="text-sm text-gray-700 dark:text-gray-300 mt-1 leading-relaxed">
                                {jobCompletion.business_notes}
                              </p>
                            </div>
                          )}
                        </div>
                      ) : (
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                          Influencer još uvek nije označio rad kao završen.
                        </p>
                      )}
                    </div>
                  </div>
                )}
            </div>
          </div>
        </PaywallOverlay>

        {/* Modals */}
        {jobCompletion && (
          <>
            <ApproveJobModal
              isOpen={showApproveModal}
              onClose={() => setShowApproveModal(false)}
              jobCompletionId={jobCompletion.id}
              influencerName={
                getDisplayName(jobCompletion.influencer_profile) !==
                'Ime i prezime skriveno'
                  ? getDisplayName(jobCompletion.influencer_profile)
                  : jobCompletion.influencer_profile?.username || 'Influencer'
              }
              onSuccess={() => {
                setShowApproveModal(false);
                loadJobCompletion(application.id);
              }}
            />
            <RejectJobModal
              isOpen={showRejectModal}
              onClose={() => setShowRejectModal(false)}
              jobCompletionId={jobCompletion.id}
              influencerName={
                getDisplayName(jobCompletion.influencer_profile) !==
                'Ime i prezime skriveno'
                  ? getDisplayName(jobCompletion.influencer_profile)
                  : jobCompletion.influencer_profile?.username || 'Influencer'
              }
              onSuccess={() => {
                setShowRejectModal(false);
                loadJobCompletion(application.id);
              }}
            />
          </>
        )}

      </div>
    </DashboardLayout>
  );
}
