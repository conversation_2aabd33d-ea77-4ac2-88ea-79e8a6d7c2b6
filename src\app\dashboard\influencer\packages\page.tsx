'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getInfluencer } from '@/lib/profiles';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, Loader2, Crown } from "lucide-react";

export default function InfluencerPackagesPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [influencer, setInfluencer] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadInfluencer();
    }
  }, [user]);

  const loadInfluencer = async () => {
    try {
      setLoading(true);
      const { data, error } = await getInfluencer(user!.id);

      if (error || !data) {
        router.push('/profil/kreiranje/influencer');
        return;
      }

      setInfluencer(data);
    } catch (err) {
      console.error('Error loading influencer data:', err);
    } finally {
      setLoading(false);
    }
  };

  const plans = [
    {
      name: "FREE",
      price: "€0",
      period: "zauvijek",
      description: "Savršen za početak",
      features: [
        "Do 5 prijava na kampanje mjesečno",
        "Osnovne analitike",
        "Email podrška",
        "Standardni profil",
        "Pregled kampanja",
        "Osnovno praćenje performansi",
      ],
      buttonText: "Trenutni plan",
      buttonVariant: "outline" as const,
      popular: false,
      current: true,
    },
    {
      name: "Premium",
      price: "€19,99",
      period: "mjesečno",
      description: "Sve što trebate za rast",
      features: [
        "Neograničene prijave na kampanje",
        "Mogućnost primanja custom ponuda",
        "Napredne analitike i insights",
        "Prioritetna podrška",
        "Verified influencer badge",
        "Alati za praćenje performansi",
        "Direktan chat sa biznsima",
        "Premium profil listing",
        "Detaljne statistike reach-a",
        "Export izvještaja",
      ],
      buttonText: "Upgradiraj na Premium",
      buttonVariant: "default" as const,
      popular: true,
    },
    {
      name: "VIP",
      price: "Uskoro",
      period: "",
      description: "Za top influencere i kreatore",
      features: [
        "Sve u Premium planu",
        "VIP badge i prioritet",
        "Ekskluzivni pristup premium kampanjama",
        "Dedicirani account manager",
        "Brand partnership consulting",
        "Dodatni marketing tools",
        "API pristup za external integracije",
      ],
      buttonText: "Pridružite se listi čekanja",
      buttonVariant: "outline" as const,
      popular: false,
      comingSoon: true,
    },
  ];

  if (loading) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  const currentSubscriptionType = influencer?.subscription_type || 'free';
  const isPremium = currentSubscriptionType === 'premium';

  return (
    <DashboardLayout requiredUserType="influencer">
      <div className="space-y-8">
        {/* Header Section */}
        <div className="text-center">
          <div className="flex items-center justify-center gap-3 mb-4">
            <h1 className="text-4xl font-bold text-foreground">
              Premium Paketi
            </h1>
            {isPremium && <Crown className="h-8 w-8 text-yellow-500" />}
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Odaberite plan koji najbolje odgovara vašim potrebama. Povećajte svoju vidljivost i zaradite više.
          </p>
          {isPremium && (
            <div className="mt-4 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg inline-block">
              <p className="text-yellow-800 font-medium">
                🎉 Vi ste Premium korisnik! Uživajte u svim naprednim funkcijama.
              </p>
            </div>
          )}
        </div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => {
            const getCardGradient = (index: number) => {
              switch (index) {
                case 0:
                  return "bg-gradient-to-b from-[#FCCBF0] via-[#FF5A57] via-[#E02F75] to-[#6700A3]"
                case 1:
                  return "bg-gradient-to-b from-[#FF5A57] via-[#E02F75] via-[#6700A3] to-[#050C38]"
                case 2:
                  return "bg-gradient-to-b from-[#6700A3] via-[#1B2062] to-[#050C38]"
                default:
                  return "bg-card/95"
              }
            }

            const getTextClasses = (index: number) => {
              return {
                title: "text-white drop-shadow-lg font-black tracking-tight",
                description: "text-white/95 drop-shadow-md font-semibold",
                price: "text-white drop-shadow-lg font-black tracking-tighter",
                period: "text-white/90 drop-shadow-md font-medium",
                feature: "text-white/95 drop-shadow-sm font-medium",
              }
            }

            const textClasses = getTextClasses(index)
            const isCurrentPlan = (plan.name === 'FREE' && !isPremium) || (plan.name === 'Premium' && isPremium)

            return (
              <Card
                key={plan.name}
                className={`relative transition-all duration-300 hover:scale-105 ${getCardGradient(index)} backdrop-blur-sm ${
                  plan.popular ? "border-accent shadow-2xl scale-105" : "hover:shadow-xl"
                } ${plan.comingSoon ? "opacity-90" : ""} ${isCurrentPlan ? "ring-4 ring-primary/50" : ""} flex flex-col h-full`}
              >
                {plan.popular && (
                  <Badge className="absolute -top-3 right-4 bg-white/20 text-white px-3 py-1 text-sm font-semibold backdrop-blur-sm drop-shadow-md">
                    Najpopularniji
                  </Badge>
                )}


                <CardHeader className="text-center pb-4">
                  <CardTitle className={`text-3xl ${textClasses.title}`}>{plan.name}</CardTitle>
                  <CardDescription className={textClasses.description}>{plan.description}</CardDescription>
                </CardHeader>

                <CardContent className="text-center pb-6 flex-1 flex flex-col">
                  <div className="mb-6">
                    <span className={`text-5xl ${textClasses.price} ${plan.comingSoon ? "opacity-70" : ""}`}>
                      {plan.price}
                    </span>
                    {plan.period && <span className={`${textClasses.period} ml-2`}>{plan.period}</span>}
                  </div>

                  <ul className="space-y-3 text-left flex-1">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start gap-3">
                        <Check className="w-5 h-5 mt-0.5 flex-shrink-0 text-white drop-shadow-sm" />
                        <span className={`text-sm ${textClasses.feature}`}>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>

                <CardFooter className="mt-auto">
                  <Button
                    className={`w-full font-bold text-base ${
                      plan.popular
                        ? "bg-white/25 hover:bg-white/35 text-white border-white/40 shadow-lg"
                        : "bg-white/20 hover:bg-white/30 text-white border-white/30 shadow-md"
                    } ${plan.comingSoon || isCurrentPlan ? "opacity-75" : ""} backdrop-blur-sm drop-shadow-md transition-all duration-200`}
                    variant="outline"
                    disabled={plan.comingSoon || isCurrentPlan}
                    onClick={() => {
                      if (plan.name === 'Premium' && !isPremium) {
                        // TODO: Implementirati Stripe integraciju ili drugi payment provider
                        alert('Upgrade funkcionalnost će biti implementirana uskoro. Molimo kontaktirajte podršku.');
                      }
                    }}
                  >
                    {isCurrentPlan ? "Trenutni plan" : plan.buttonText}
                  </Button>
                </CardFooter>
              </Card>
            )
          })}
        </div>

        {/* Footer Section */}
        <div className="text-center">
          <p className="text-muted-foreground mb-4">Svi planovi uključuju osnovne funkcije i pristup mobilnoj aplikaciji</p>
          <div className="flex justify-center gap-6 text-sm">
            <a href="/uslovi-koristenja" className="text-muted-foreground hover:text-foreground transition-colors">
              Uslovi korištenja
            </a>
            <a href="/politika-privatnosti" className="text-muted-foreground hover:text-foreground transition-colors">
              Politika privatnosti
            </a>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}