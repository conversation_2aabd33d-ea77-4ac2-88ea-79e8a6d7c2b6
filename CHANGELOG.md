# Changelog - InfluConnect

Sve značajne promjene u ovom projektu će biti dokumentovane u ovom fajlu.

## [Unreleased]

### Planirано
- Biznis profil kreiranje forma
- Biznis dashboard stranica
- Kampanje sistem (kreiranje, pretraga, aplikacije)
- Real-time chat sistem
- Stripe Connect integracija

## [0.2.0] - 2025-01-21

### ✅ Dodano
- **Kompletna autentifikacija**
  - Registracija za influencere i biznise
  - Email verifikacija
  - Login/logout funkcionalnost
  - Auth Context za React
- **Influencer profil sistem**
  - Kreiranje profila sa društvenim mrežama
  - Postavljanje cijena za sadržaj
  - Portfolio upload mogućnost
  - Validacija svih polja
- **Dashboard sistem**
  - Osnovni dashboard sa preusmeravanjem
  - Influencer dashboard sa statistikama
  - Pregled profila i brzih akcija
- **Database schema**
  - Profiles, influencers, businesses tabele
  - RLS (Row Level Security) policies
  - Auto-trigger za kreiranje profila (uklonjen zbog problema)
  - Helper funkcije za rad sa profilima

### 🔧 Ispravke
- Ispravljen 406 error pri ažuriranju profila
- Dodana upsertProfile funkcija za kreiranje profila ako ne postoji
- Poboljšan auth flow - registracija → profil kreiranje → dashboard
- Bolje error handling u svim formama

### 🎨 UI/UX
- Mobile-first responsive dizajn
- Shadcn/ui komponente (Button, Card, Input, Label, Textarea)
- Loading states i error handling
- Touch-friendly interface (44px minimum touch targets)

## [0.1.0] - 2025-01-21

### ✅ Dodano
- **Početni setup**
  - Next.js 14 sa App Router
  - TypeScript konfiguracija
  - Tailwind CSS + Shadcn/ui
  - ESLint + Prettier
- **UX/UI Dizajn**
  - Kompletni wireframes za sve stranice
  - User flow dijagrami
  - Design sistem sa paletom boja i tipografijom
  - InfluConnect branding i vizuelni identitet
- **Landing stranica**
  - Hero sekcija sa CTA dugmadima
  - "Kako funkcioniše" sekcija
  - Benefiti za influencere i biznise
  - Responsive dizajn
- **Supabase setup**
  - Database konfiguracija
  - Environment varijable
  - TypeScript tipovi za database
- **Dokumentacija**
  - Detaljni plan razvoja
  - Wireframes i user flows
  - Design sistem dokumentacija
  - README sa instrukcijama

### 🛠️ Tehnički stack
- **Frontend**: Next.js 14, TypeScript, Tailwind CSS, Shadcn/ui
- **Backend**: Supabase (PostgreSQL, Auth, Real-time, Storage)
- **Deployment**: Vercel (pripremljen)
- **Version Control**: Git sa strukturiranim commit-ima

## Konvencije

### Commit Messages
- `feat:` - Nova funkcionalnost
- `fix:` - Ispravka greške
- `docs:` - Dokumentacija
- `style:` - Formatiranje koda
- `refactor:` - Refaktorisanje koda
- `test:` - Dodavanje testova
- `chore:` - Održavanje

### Versioning
Koristimo [Semantic Versioning](https://semver.org/):
- MAJOR.MINOR.PATCH
- MAJOR: Breaking changes
- MINOR: Nova funkcionalnost (backward compatible)
- PATCH: Bug fixes (backward compatible)
