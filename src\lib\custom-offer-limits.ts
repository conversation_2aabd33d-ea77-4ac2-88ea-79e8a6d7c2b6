import { supabase } from './supabase';

export interface CustomOfferLimitResult {
  canSend: boolean;
  remainingOffers: number;
  totalOffersThisMonth: number;
  resetDate: string;
  isFreePlan: boolean;
}

/**
 * Računa mjesečni period od datuma kada je korisnik završio onboarding
 * @param onboardingCompletedDate Datum kada je završen onboarding
 * @returns Početni i završni datum trenutnog mjeseca od onboarding datuma
 */
function getMonthlyPeriodFromOnboarding(onboardingCompletedDate: string): {
  periodStart: Date;
  periodEnd: Date;
} {
  const onboardingDate = new Date(onboardingCompletedDate);
  const now = new Date();

  // Uzimamo dan u mjesecu kada je završen onboarding
  const onboardingDay = onboardingDate.getDate();

  // Kreiramo datum početka trenutnog mjeseca (isti dan kao onboarding)
  const periodStart = new Date(
    now.getFullYear(),
    now.getMonth(),
    onboardingDay
  );

  // Ako je trenutni datum prije onboarding dana u mjesecu,
  // onda je period počeo prošlog mjeseca
  if (now.getDate() < onboardingDay) {
    periodStart.setMonth(periodStart.getMonth() - 1);
  }

  // Kraj perioda je jedan dan prije sljedećeg onboarding dana
  const periodEnd = new Date(periodStart);
  periodEnd.setMonth(periodEnd.getMonth() + 1);
  periodEnd.setDate(periodEnd.getDate() - 1);
  periodEnd.setHours(23, 59, 59, 999);

  return { periodStart, periodEnd };
}

/**
 * Provjerava da li free business korisnik može poslati custom ponudu
 * @param businessId ID business korisnika
 * @returns Objekt sa informacijama o limitu
 */
export async function checkCustomOfferLimit(
  businessId: string
): Promise<CustomOfferLimitResult> {
  const FREE_PLAN_MONTHLY_LIMIT = 3;

  try {
    // Dohvatamo business podatke sa subscription_type i profile podatke
    const { data: businessData, error: businessError } = await supabase
      .from('businesses')
      .select(
        `
        subscription_type,
        profiles!inner(
          onboarding_completed,
          created_at,
          onboarding_completed_at
        )
      `
      )
      .eq('id', businessId)
      .single();

    if (businessError || !businessData) {
      throw new Error('Business profil nije pronađen');
    }

    const subscriptionType = businessData.subscription_type || 'free';
    const isFreePlan = subscriptionType === 'free';

    // Ako je premium plan, nema ograničenja
    if (!isFreePlan) {
      return {
        canSend: true,
        remainingOffers: Infinity,
        totalOffersThisMonth: 0,
        resetDate: '',
        isFreePlan: false,
      };
    }

    // Za free plan, provjeravamo da li je onboarding završen
    const profile = businessData.profiles;
    if (!profile.onboarding_completed) {
      throw new Error('Onboarding nije završen');
    }

    // Koristimo onboarding_completed_at ako postoji, inače fallback na created_at
    const onboardingDate = profile.onboarding_completed_at || profile.created_at;
    if (!onboardingDate) {
      throw new Error('Datum onboarding-a nije pronađen');
    }

    // Računamo trenutni mjesečni period
    const { periodStart, periodEnd } =
      getMonthlyPeriodFromOnboarding(onboardingDate);

    // Brojimo custom ponude u trenutnom mjesečnom periodu
    const { data: offers, error: offersError } = await supabase
      .from('direct_offers')
      .select('id, created_at')
      .eq('business_id', businessId)
      .eq('offer_type', 'custom')
      .gte('created_at', periodStart.toISOString())
      .lte('created_at', periodEnd.toISOString());

    if (offersError) {
      throw new Error('Greška pri dohvaćanju ponuda');
    }

    const totalOffersThisMonth = offers?.length || 0;
    const remainingOffers = Math.max(
      0,
      FREE_PLAN_MONTHLY_LIMIT - totalOffersThisMonth
    );
    const canSend = remainingOffers > 0;

    // Sljedeći reset datum
    const nextResetDate = new Date(periodEnd);
    nextResetDate.setDate(nextResetDate.getDate() + 1);
    nextResetDate.setHours(0, 0, 0, 0);

    return {
      canSend,
      remainingOffers,
      totalOffersThisMonth,
      resetDate: nextResetDate.toISOString(),
      isFreePlan: true,
    };
  } catch (error) {
    console.error('Greška pri provjeri limita custom ponuda:', error);
    // U slučaju greške, dozvoljavamo slanje ponude da ne blokiranje korisnika
    return {
      canSend: true,
      remainingOffers: 0,
      totalOffersThisMonth: 0,
      resetDate: '',
      isFreePlan: true,
    };
  }
}

/**
 * Formatira datum reset-a u human-readable format
 * @param resetDate ISO string datuma
 * @returns Formatiran datum
 */
export function formatResetDate(resetDate: string): string {
  const date = new Date(resetDate);
  const now = new Date();

  const diffTime = date.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) {
    return 'sutra';
  } else if (diffDays <= 7) {
    return `za ${diffDays} dana`;
  } else {
    return date.toLocaleDateString('sr-Latn-BA', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  }
}
