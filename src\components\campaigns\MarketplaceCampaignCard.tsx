import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import {
  Calendar,
  Users,
  Crown,
} from 'lucide-react';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import { useRouter } from 'next/navigation';

interface Platform {
  name: string;
  icon: string;
  content_types: string[];
}

interface Campaign {
  id: string;
  title: string;
  description: string;
  budget: number;
  status: string;
  location: string | null;
  application_deadline: string | null;
  min_followers: number | null;
  max_followers: number | null;
  age_range_min: number | null;
  age_range_max: number | null;
  gender: string | null;
  is_featured: boolean;
  created_at: string;
  business_id: string;
  content_types: string[] | null;
  platforms?: Platform[];
  applications_count?: number;
  businesses?: {
    company_name: string;
    industry: string;
  };
}

interface MarketplaceCampaignCardProps {
  campaign: Campaign;
  isPremium?: boolean;
}

const MarketplaceCampaignCard: React.FC<MarketplaceCampaignCardProps> = ({
  campaign,
  isPremium = false,
}) => {
  const router = useRouter();

  const handleClick = () => {
    router.push(`/campaigns/${campaign.id}`);
  };

  const getContentTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      post: 'Photo Feed Post',
      story: 'Story',
      reel: 'Reel',
      video: 'Video',
      blog: 'Blog Post',
    };
    return labels[type] || type;
  };



  const formattedDate = new Date(campaign.created_at).toLocaleDateString("de-DE", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });

  const formattedPrice = `${campaign.budget.toLocaleString('de-DE')} €`;

  const cardStyles = isPremium
    ? "w-full h-[360px] bg-gradient-to-br from-amber-50 via-amber-100/80 to-yellow-200/60 backdrop-blur-sm border-2 border-amber-300/60 shadow-lg shadow-amber-200/50 relative overflow-hidden cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:shadow-xl hover:shadow-amber-300/60 flex flex-col"
    : "w-full h-[360px] bg-gradient-to-br from-pink-50/80 to-rose-100/60 backdrop-blur-sm border border-pink-200/50 shadow-lg shadow-pink-100/50 relative overflow-hidden cursor-pointer transition-all duration-300 hover:scale-[1.02] flex flex-col";

  return (
    <Card className={cardStyles} onClick={handleClick}>
      {/* Premium golden overlay */}
      {isPremium && (
        <div className="absolute inset-0 bg-gradient-to-br from-amber-100/40 via-yellow-100/30 to-amber-200/40 pointer-events-none" />
      )}

      {/* Premium badge and price section */}
      <div className="flex justify-between items-start relative z-10">
        {/* Premium badge on the left - goes to edge */}
        {isPremium && (
          <div className="px-3 py-1 rounded-r-full shadow-lg flex items-center gap-1 bg-amber-600 text-white">
            <Crown className="h-3 w-3" />
            <div className="font-bold text-xs whitespace-nowrap">
              PREMIUM
            </div>
          </div>
        )}

        {/* Price on the right - goes to edge */}
        <div className={`px-3 py-1 rounded-l-full shadow-lg flex items-center gap-1 ml-auto ${
          isPremium
            ? 'bg-amber-600 text-white'
            : 'bg-emerald-600 text-white'
        }`}>
          <div className="font-bold text-xs whitespace-nowrap">
            {formattedPrice}
          </div>
        </div>
      </div>

      <CardContent className="px-3 pt-2 pb-4 relative z-10 flex-1 flex flex-col">
        {/* Title - EXACTLY 2 rows reserved */}
        <div className="h-12 mb-3">
          <h3 className="font-semibold text-base leading-6 line-clamp-2">
            {campaign.title}
          </h3>
        </div>

        {/* Description - EXACTLY 2 rows reserved */}
        <div className="h-10 mb-3">
          <p className="text-sm text-muted-foreground leading-5 line-clamp-2">
            {campaign.description}
          </p>
        </div>

        {/* Platforms - EXACTLY 3 rows reserved */}
        <div className="flex-1 mb-3">
          <h4 className="text-xs font-medium text-foreground/80 mb-2">Traženi sadržaj:</h4>
          <div className="h-20 space-y-1.5">
            {/* Platform 1 */}
            <div className="h-6">
              {campaign.platforms && campaign.platforms[0] && (
                <div className="flex items-center gap-2">
                  <div className={`flex items-center gap-1.5 text-xs font-medium px-2 py-1 rounded-full ${
                    campaign.platforms[0].name === 'Instagram'
                      ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-900 border border-purple-200/50 backdrop-blur-sm'
                      : campaign.platforms[0].name === 'YouTube'
                      ? 'bg-red-500/20 text-red-900 border border-red-200/50 backdrop-blur-sm'
                      : campaign.platforms[0].name === 'TikTok'
                      ? 'bg-gray-500/20 text-gray-900 border border-gray-200/50 backdrop-blur-sm'
                      : 'bg-gray-500/20 text-gray-900 border border-gray-200/50 backdrop-blur-sm'
                  }`}>
                    <PlatformIconSimple platform={campaign.platforms[0].name} size="md" />
                    <span className="sr-only">{campaign.platforms[0].name}</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {campaign.platforms[0].content_types?.slice(0, 3).map((contentType, contentIndex) => (
                      <span
                        key={contentIndex}
                        className={`text-xs px-1.5 py-0.5 rounded border backdrop-blur-sm ${
                          isPremium
                            ? 'bg-amber-100/60 text-amber-800 border-amber-200/50'
                            : 'bg-pink-100/60 text-pink-800 border-pink-200/50'
                        }`}
                      >
                        {getContentTypeLabel(contentType)}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Platform 2 */}
            <div className="h-6">
              {campaign.platforms && campaign.platforms[1] && (
                <div className="flex items-center gap-2">
                  <div className={`flex items-center gap-1.5 text-xs font-medium px-2 py-1 rounded-full ${
                    campaign.platforms[1].name === 'Instagram'
                      ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-900 border border-purple-200/50 backdrop-blur-sm'
                      : campaign.platforms[1].name === 'YouTube'
                      ? 'bg-red-500/20 text-red-900 border border-red-200/50 backdrop-blur-sm'
                      : campaign.platforms[1].name === 'TikTok'
                      ? 'bg-gray-500/20 text-gray-900 border border-gray-200/50 backdrop-blur-sm'
                      : 'bg-gray-500/20 text-gray-900 border border-gray-200/50 backdrop-blur-sm'
                  }`}>
                    <PlatformIconSimple platform={campaign.platforms[1].name} size="md" />
                    <span className="sr-only">{campaign.platforms[1].name}</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {campaign.platforms[1].content_types?.slice(0, 3).map((contentType, contentIndex) => (
                      <span
                        key={contentIndex}
                        className={`text-xs px-1.5 py-0.5 rounded border backdrop-blur-sm ${
                          isPremium
                            ? 'bg-amber-100/60 text-amber-800 border-amber-200/50'
                            : 'bg-pink-100/60 text-pink-800 border-pink-200/50'
                        }`}
                      >
                        {getContentTypeLabel(contentType)}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Platform 3 */}
            <div className="h-6">
              {campaign.platforms && campaign.platforms[2] && (
                <div className="flex items-center gap-2">
                  <div className={`flex items-center gap-1.5 text-xs font-medium px-2 py-1 rounded-full ${
                    campaign.platforms[2].name === 'Instagram'
                      ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-900 border border-purple-200/50 backdrop-blur-sm'
                      : campaign.platforms[2].name === 'YouTube'
                      ? 'bg-red-500/20 text-red-900 border border-red-200/50 backdrop-blur-sm'
                      : campaign.platforms[2].name === 'TikTok'
                      ? 'bg-gray-500/20 text-gray-900 border border-gray-200/50 backdrop-blur-sm'
                      : 'bg-gray-500/20 text-gray-900 border border-gray-200/50 backdrop-blur-sm'
                  }`}>
                    <PlatformIconSimple platform={campaign.platforms[2].name} size="md" />
                    <span className="sr-only">{campaign.platforms[2].name}</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {campaign.platforms[2].content_types?.slice(0, 3).map((contentType, contentIndex) => (
                      <span
                        key={contentIndex}
                        className={`text-xs px-1.5 py-0.5 rounded border backdrop-blur-sm ${
                          isPremium
                            ? 'bg-amber-100/60 text-amber-800 border-amber-200/50'
                            : 'bg-pink-100/60 text-pink-800 border-pink-200/50'
                        }`}
                      >
                        {getContentTypeLabel(contentType)}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Date and applications count - ALWAYS at the bottom */}
        <div className="flex items-center justify-between text-xs text-muted-foreground pt-3 pb-1 border-t border-gray-200/30 mt-auto">
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            {formattedDate}
          </div>
          <div className="flex items-center gap-1">
            <Users className="h-3 w-3" />
            {typeof campaign.applications_count === 'number' ? campaign.applications_count : 0}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MarketplaceCampaignCard;
