'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { User, Crown, ArrowRight, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  getBusinessApplicationsCards,
  getBusinessApplicationsStats,
} from '@/lib/campaigns';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { GradientTabs } from '@/components/ui/gradient-tabs';
import ApplicationCard from '@/components/campaigns/ApplicationCard';
import { canViewApplications } from '@/lib/subscriptions';
import { SubscriptionDebugger } from '@/components/debug/SubscriptionDebugger';

interface Application {
  id: string;
  campaign_id: string;
  influencer_id: string;
  status: 'pending' | 'accepted' | 'rejected';
  proposed_rate: number;
  applied_at: string;
  campaign_title: string;
  campaign_budget: number;
  influencer_username: string;
  influencer_display_name: string;
  influencer_avatar_url: string | null;
  delivery_timeframe: string;
  proposal_text: string;
}

export default function ApplicationsPage() {
  const { user } = useAuth();
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [userSubscriptionType, setUserSubscriptionType] = useState<
    'free' | 'premium'
  >('free');
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    accepted: 0,
    rejected: 0,
  });

  useEffect(() => {
    if (!user) return;

    const loadApplicationsAndSubscription = async () => {
      try {
        // Load user subscription type first
        const { supabase } = await import('@/lib/supabase');
        const { data: business } = await supabase
          .from('businesses')
          .select('subscription_type')
          .eq('id', user.id)
          .single();

        if (business) {
          setUserSubscriptionType(
            (business.subscription_type as 'free' | 'premium') || 'free'
          );
        }

        // Load applications with optimized function
        const [applicationsResult, statsResult] = await Promise.all([
          getBusinessApplicationsCards(
            user.id,
            activeTab === 'all' ? undefined : activeTab
          ),
          getBusinessApplicationsStats(user.id),
        ]);

        if (applicationsResult.error) {
          console.error(
            'Error loading applications:',
            applicationsResult.error
          );
        } else {
          setApplications(applicationsResult.data || []);
        }

        if (statsResult.error) {
          console.error('Error loading stats:', statsResult.error);
        } else if (statsResult.data) {
          setStats({
            total: statsResult.data.total_count,
            pending: statsResult.data.pending_count,
            accepted: statsResult.data.accepted_count,
            rejected: statsResult.data.rejected_count,
          });
        }
      } catch (error) {
        console.error('Error loading applications:', error);
      } finally {
        setLoading(false);
      }
    };

    loadApplicationsAndSubscription();
  }, [user, activeTab]);

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbačeno';
      default:
        return status;
    }
  };

  // Applications are already filtered by activeTab in useEffect

  if (loading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <SubscriptionDebugger />
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">
            Aplikacije na kampanje
          </h1>
          <p className="text-muted-foreground mt-1">
            Pregledajte i upravljajte aplikacijama influencera na vaše kampanje
          </p>
        </div>

        {/* Gradient Tabs */}
        <GradientTabs
          tabs={[
            { name: 'Sve', value: 'all', count: stats.total },
            { name: 'Na čekanju', value: 'pending', count: stats.pending },
            { name: 'Prihvaćeno', value: 'accepted', count: stats.accepted },
            { name: 'Odbačeno', value: 'rejected', count: stats.rejected },
          ]}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          className="mb-6"
        />

        {/* Applications List */}
        {!canViewApplications(userSubscriptionType) && stats.total > 0 ? (
          <div className="relative min-h-[500px]">
            {/* Blurred applications in background */}
            <div className="filter blur-sm opacity-40 pointer-events-none select-none absolute inset-0 overflow-hidden">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {applications.map(application => (
                  <ApplicationCard
                    key={application.id}
                    application={{
                      ...application,
                      // Map new interface to old ApplicationCard props
                      campaigns: {
                        id: application.campaign_id,
                        title: application.campaign_title,
                        budget: application.campaign_budget,
                        business_id: user?.id || '',
                      },
                      profiles: {
                        id: application.influencer_id,
                        username: application.influencer_username,
                        full_name: application.influencer_display_name,
                        public_display_name:
                          application.influencer_display_name,
                        avatar_url: application.influencer_avatar_url,
                      },
                      proposal_text: application.proposal_text,
                      delivery_timeframe: application.delivery_timeframe,
                      portfolio_links: null,
                      experience_relevant: null,
                      audience_insights: null,
                    }}
                  />
                ))}
              </div>
            </div>

            {/* Overlay */}
            <div className="absolute inset-0 bg-white/30 backdrop-blur-sm flex items-center justify-center p-6 z-10">
              <div className="max-w-md w-full bg-white rounded-xl shadow-lg border p-6 text-center">
                {/* Icon */}
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-yellow-100 to-orange-100">
                  <Crown className="h-8 w-8 text-yellow-600" />
                </div>

                {/* Title and Description */}
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Premium plan potreban
                </h3>
                <p className="text-gray-600 mb-6">
                  Da biste vidjeli prijave na kampanje morate biti Premium
                  korisnik
                </p>

                {/* Benefits List */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">
                    Premium prednosti:
                  </h4>
                  <ul className="space-y-2 text-left">
                    {[
                      'Pristup svim prijavama na kampanje',
                      'Detaljni influencer profili',
                      'Chat sa influencerima',
                      'Napredni filteri i pretragu',
                      'Export podataka u CSV/Excel',
                      'Prioritetna podrška',
                    ].map((benefit, index) => (
                      <li
                        key={index}
                        className="flex items-center text-sm text-gray-600"
                      >
                        <CheckCircle className="mr-2 h-4 w-4 text-green-500 flex-shrink-0" />
                        <span>{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* CTA Button */}
                <Button
                  onClick={async () => {
                    // Development simulation: Upgrade user to premium
                    if (process.env.NODE_ENV === 'development') {
                      try {
                        const { supabase } = await import('@/lib/supabase');

                        // Get current user from localStorage
                        const userStr = localStorage.getItem(
                          'sb-localhost-auth-token'
                        );
                        if (userStr) {
                          const authData = JSON.parse(userStr);
                          const userId = authData?.user?.id;

                          if (userId) {
                            // Update user to premium
                            const { error } = await supabase
                              .from('businesses')
                              .update({ subscription_type: 'premium' })
                              .eq('id', userId);

                            if (!error) {
                              alert(
                                '🎉 Simulacija uspješna! Korisnik je sada Premium. Refresh stranicu.'
                              );
                              window.location.reload();
                              return;
                            }
                          }
                        }

                        alert(
                          '❌ Greška pri simulaciji premium upgrade-a. Proverite console za detalje.'
                        );
                        console.error('Failed to simulate premium upgrade');
                      } catch (error) {
                        console.error(
                          'Error simulating premium upgrade:',
                          error
                        );
                        alert(
                          '❌ Greška pri simulaciji premium upgrade-a. Proverite console za detalje.'
                        );
                      }
                    } else {
                      // Production: Redirect to pricing/upgrade page
                      console.log('Redirecting to upgrade page...');
                    }
                  }}
                  className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2"
                >
                  Pretplatite se
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {applications.length === 0 ? (
              <div className="text-center py-12">
                <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nema aplikacija
                </h3>
                <p className="text-gray-600">
                  {activeTab === 'all'
                    ? 'Još uvijek nema aplikacija na vaše kampanje.'
                    : `Nema aplikacija sa statusom "${getStatusText(activeTab)}".`}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {applications.map(application => (
                  <ApplicationCard
                    key={application.id}
                    application={{
                      ...application,
                      // Map new interface to old ApplicationCard props
                      campaigns: {
                        id: application.campaign_id,
                        title: application.campaign_title,
                        budget: application.campaign_budget,
                        business_id: user?.id || '',
                      },
                      profiles: {
                        id: application.influencer_id,
                        username: application.influencer_username,
                        full_name: application.influencer_display_name,
                        public_display_name:
                          application.influencer_display_name,
                        avatar_url: application.influencer_avatar_url,
                      },
                      proposal_text: application.proposal_text,
                      delivery_timeframe: application.delivery_timeframe,
                      portfolio_links: null,
                      experience_relevant: null,
                      audience_insights: null,
                    }}
                  />
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
