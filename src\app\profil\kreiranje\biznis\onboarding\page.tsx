'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { LogOut } from 'lucide-react';
import { UsernameStep } from '@/components/onboarding/steps/UsernameStep';
import { CategoriesStep } from '@/components/onboarding/steps/CategoriesStep';
import { BusinessCountryStep } from '@/components/onboarding/steps/BusinessCountryStep';
import { BusinessNameStep } from '@/components/onboarding/steps/BusinessNameStep';
import { BusinessBioStep } from '@/components/onboarding/steps/BusinessBioStep';
import { BusinessSocialMediaStep } from '@/components/onboarding/steps/BusinessSocialMediaStep';
import { CityStep } from '@/components/onboarding/steps/CityStep';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';

interface OnboardingData {
  username: string;
  categories: number[];
  country: string;
  brandName: string; // Changed from businessName to brandName for clarity
  bio: string;
  socialMedia: {
    instagram_handle?: string;
    instagram_followers?: number;
    tiktok_handle?: string;
    tiktok_followers?: number;
    youtube_handle?: string;
    youtube_subscribers?: number;
  };
  city: string;
}

const TOTAL_STEPS = 7;

const ONBOARDING_STEPS = [
  { id: 1, title: 'Username', description: 'Odaberite korisničko ime' },
  { id: 2, title: 'Kategorije', description: 'Odaberite djelatnost/branšu' },
  { id: 3, title: 'Država', description: 'Odaberite vašu državu' },
  { id: 4, title: 'Naziv firme', description: 'Unesite naziv vašeg brenda' },
  { id: 5, title: 'Opis firme', description: 'Opišite vašu firmu' },
  { id: 6, title: 'Društvene mreže', description: 'Dodajte vaše profile' },
  { id: 7, title: 'Lokacija', description: 'Unesite vaš grad' },
];

export default function BusinessOnboardingPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, signOut } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    username: '',
    categories: [],
    country: '',
    brandName: '',
    bio: '',
    socialMedia: {},
    city: '',
  });

  useEffect(() => {
    if (!user) {
      router.push('/prijava');
      return;
    }

    // Load existing onboarding data and progress
    loadOnboardingProgress();
  }, [user, router]);

  const loadOnboardingProgress = async () => {
    if (!user) return;

    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error loading profile:', error);
        return;
      }

      if (profile) {
        // Load brand name from businesses table, not from profile.full_name
        const { data: business } = await supabase
          .from('businesses')
          .select('company_name')
          .eq('id', user.id)
          .single();

        // Load existing data
        const existingData: Partial<OnboardingData> = {
          username: profile.username || '',
          brandName: business?.company_name || '', // Load from businesses.company_name instead of profile.full_name
          bio: profile.bio || '',
          country: profile.country || '',
          city: profile.city || '',
        };

        // Load categories from business_target_categories (always check, not just when completed)
        const { data: categories } = await supabase
          .from('business_target_categories')
          .select('category_id')
          .eq('business_id', user.id);

        if (categories && categories.length > 0) {
          existingData.categories = categories.map(c => c.category_id);
        }

        // Load social media from business_platforms (always check, not just when completed)
        const { data: platforms } = await supabase
          .from('business_platforms')
          .select('*')
          .eq('business_id', user.id);

        if (platforms && platforms.length > 0) {
          const socialMedia: any = {};
          platforms.forEach(p => {
            if (p.platform_id === 1) {
              // Instagram
              socialMedia.instagram_handle = p.handle;
              socialMedia.instagram_followers = p.followers_count;
            } else if (p.platform_id === 2) {
              // TikTok
              socialMedia.tiktok_handle = p.handle;
              socialMedia.tiktok_followers = p.followers_count;
            } else if (p.platform_id === 3) {
              // YouTube
              socialMedia.youtube_handle = p.handle;
              socialMedia.youtube_subscribers = p.followers_count;
            }
          });
          existingData.socialMedia = socialMedia;
        }

        setOnboardingData(prev => ({ ...prev, ...existingData }));

        // Set current step from URL or profile
        const stepParam = searchParams.get('step');
        if (stepParam) {
          const step = parseInt(stepParam);
          if (step >= 1 && step <= TOTAL_STEPS) {
            setCurrentStep(step);
          }
        } else if (profile.onboarding_step && !profile.profile_completed) {
          setCurrentStep(profile.onboarding_step);
        }
      }
    } catch (error) {
      console.error('Error loading onboarding progress:', error);
    }
  };

  const updateData = (stepData: Partial<OnboardingData>) => {
    setOnboardingData(prev => ({ ...prev, ...stepData }));
  };

  const saveOnboardingProgress = async (
    step: number,
    data?: Partial<OnboardingData>
  ) => {
    if (!user) return;

    try {
      const dataToSave = data || onboardingData;

      // Always update the current step
      const profileUpdates: any = {
        onboarding_step: step,
      };

      // Save step-specific data
      if (step >= 1 && dataToSave.username) {
        profileUpdates.username = dataToSave.username;
      }

      if (step >= 3 && dataToSave.country) {
        profileUpdates.country = dataToSave.country;
      }

      if (step >= 4 && dataToSave.brandName) {
        profileUpdates.full_name = dataToSave.brandName;
      }

      if (step >= 5 && dataToSave.bio) {
        profileUpdates.bio = dataToSave.bio;
      }

      if (step >= 7 && dataToSave.city) {
        profileUpdates.city = dataToSave.city;
      }

      // Update profile
      const { error: profileError } = await supabase
        .from('profiles')
        .update(profileUpdates)
        .eq('id', user.id);

      if (profileError) {
        console.error('Error saving profile progress:', profileError);
        return;
      }

      // Ensure business record exists before saving any business-related data
      if (
        step >= 2 ||
        (dataToSave.categories && dataToSave.categories.length > 0) ||
        (step >= 4 && dataToSave.brandName)
      ) {
        await ensureBusinessRecord(dataToSave);
      }

      // Save categories if at step 2 or later
      if (
        step >= 2 &&
        dataToSave.categories &&
        dataToSave.categories.length > 0
      ) {
        // Delete existing categories first
        await supabase
          .from('business_target_categories')
          .delete()
          .eq('business_id', user.id);

        // Insert new categories
        const categoryInserts = dataToSave.categories.map(categoryId => ({
          business_id: user.id,
          category_id: categoryId,
        }));

        const { error: categoriesError } = await supabase
          .from('business_target_categories')
          .insert(categoryInserts);

        if (categoriesError) {
          console.error('Error saving categories:', categoriesError);
        }
      }

      // Save social media if at step 6 or later
      if (step >= 6 && dataToSave.socialMedia) {
        // Delete existing platforms first
        await supabase
          .from('business_platforms')
          .delete()
          .eq('business_id', user.id);

        // Insert new platforms
        const platformInserts = [];
        if (dataToSave.socialMedia.instagram_handle) {
          platformInserts.push({
            business_id: user.id,
            platform_id: 1,
            handle: dataToSave.socialMedia.instagram_handle,
            followers_count: dataToSave.socialMedia.instagram_followers || 0,
          });
        }
        if (dataToSave.socialMedia.tiktok_handle) {
          platformInserts.push({
            business_id: user.id,
            platform_id: 2,
            handle: dataToSave.socialMedia.tiktok_handle,
            followers_count: dataToSave.socialMedia.tiktok_followers || 0,
          });
        }
        if (dataToSave.socialMedia.youtube_handle) {
          platformInserts.push({
            business_id: user.id,
            platform_id: 3,
            handle: dataToSave.socialMedia.youtube_handle,
            followers_count: dataToSave.socialMedia.youtube_subscribers || 0,
          });
        }

        if (platformInserts.length > 0) {
          const { error: platformsError } = await supabase
            .from('business_platforms')
            .insert(platformInserts);

          if (platformsError) {
            console.error('Error saving platforms:', platformsError);
          }
        }
      }
    } catch (error) {
      console.error('Error saving onboarding progress:', error);
    }
  };

  const ensureBusinessRecord = async (dataToUse?: Partial<OnboardingData>) => {
    if (!user) return;

    try {
      // Check if business record exists
      const { data: existing } = await supabase
        .from('businesses')
        .select('id')
        .eq('id', user.id)
        .single();

      if (!existing) {
        const currentData = dataToUse || onboardingData;
        // Create business record
        const { error } = await supabase.from('businesses').insert({
          id: user.id,
          company_name: currentData.brandName || 'Temp Company Name',
          industry: currentData.categories?.[0] || null,
        });

        if (error) {
          console.error('Error creating business record:', error);
        }
      }
    } catch (error) {
      console.error('Error ensuring business record:', error);
    }
  };

  const nextStep = () => {
    if (currentStep < TOTAL_STEPS) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const completeOnboarding = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Update profile
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          username: onboardingData.username,
          full_name: onboardingData.brandName, // Brand name is stored in full_name
          bio: onboardingData.bio || null,
          country: onboardingData.country,
          city: onboardingData.city || null,
          user_type: 'business',
          profile_completed: true,
          onboarding_step: TOTAL_STEPS,
          onboarding_completed: true,
          onboarding_completed_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (profileError) throw profileError;

      // Upsert business record (update if exists, create if not)
      const { error: businessError } = await supabase
        .from('businesses')
        .upsert({
          id: user.id,
          company_name: onboardingData.brandName, // Use brand name for now, will be updated later
          industry: onboardingData.categories[0] || null, // Use first category as industry
        });

      if (businessError) throw businessError;

      // Handle categories - delete existing and insert new ones
      if (onboardingData.categories.length > 0) {
        // Delete existing categories first
        await supabase
          .from('business_target_categories')
          .delete()
          .eq('business_id', user.id);

        // Insert new categories
        const categoryInserts = onboardingData.categories.map(categoryId => ({
          business_id: user.id,
          category_id: categoryId,
        }));

        const { error: categoriesError } = await supabase
          .from('business_target_categories')
          .insert(categoryInserts);

        if (categoriesError) throw categoriesError;
      }

      // Handle social media platforms
      const platformInserts = [];
      if (onboardingData.socialMedia.instagram_handle) {
        platformInserts.push({
          business_id: user.id,
          platform_id: 1, // Instagram
          handle: onboardingData.socialMedia.instagram_handle,
          followers_count: onboardingData.socialMedia.instagram_followers || 0,
        });
      }
      if (onboardingData.socialMedia.tiktok_handle) {
        platformInserts.push({
          business_id: user.id,
          platform_id: 2, // TikTok
          handle: onboardingData.socialMedia.tiktok_handle,
          followers_count: onboardingData.socialMedia.tiktok_followers || 0,
        });
      }
      if (onboardingData.socialMedia.youtube_handle) {
        platformInserts.push({
          business_id: user.id,
          platform_id: 3, // YouTube
          handle: onboardingData.socialMedia.youtube_handle,
          followers_count: onboardingData.socialMedia.youtube_subscribers || 0,
        });
      }

      // Delete existing platforms first
      await supabase
        .from('business_platforms')
        .delete()
        .eq('business_id', user.id);

      // Insert new platforms if any
      if (platformInserts.length > 0) {
        const { error: platformsError } = await supabase
          .from('business_platforms')
          .insert(platformInserts);

        if (platformsError) throw platformsError;
      }

      toast.success('Profil je uspešno kreiran!');
      router.push('/dashboard/biznis');
    } catch (error) {
      console.error('Error completing onboarding:', error);
      toast.error('Greška pri kreiranju profila');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <UsernameStep
            value={onboardingData.username}
            onChange={username => updateData({ username })}
            onNext={async () => {
              await saveOnboardingProgress(2, { ...onboardingData });
              nextStep();
            }}
            title="Korisničko ime"
            description="Ovo korisničko ime će biti vidljivo influencerima na platformi"
          />
        );
      case 2:
        return (
          <CategoriesStep
            value={onboardingData.categories}
            onChange={categories => updateData({ categories })}
            onNext={async () => {
              await saveOnboardingProgress(3, { ...onboardingData });
              nextStep();
            }}
            onBack={prevStep}
            title="Odaberite djelatnost/branšu"
            description="Koje kategorije najbolje opisuju industriju u kojoj posluje vaš brend?"
            maxCategories={3}
          />
        );
      case 3:
        return (
          <BusinessCountryStep
            value={onboardingData.country}
            onChange={country => updateData({ country })}
            onNext={async () => {
              await saveOnboardingProgress(4, { ...onboardingData });
              nextStep();
            }}
            onBack={prevStep}
            title="Iz koje ste države?"
            description="Ova informacija pomaže influencerima da pronađu lokalne brendove"
          />
        );
      case 4:
        return (
          <BusinessNameStep
            value={onboardingData.brandName}
            onNext={async businessName => {
              const newData = { ...onboardingData, brandName: businessName };
              updateData({ brandName: businessName });
              await saveOnboardingProgress(5, newData);
              nextStep();
            }}
            onBack={prevStep}
          />
        );
      case 5:
        return (
          <BusinessBioStep
            value={onboardingData.bio}
            onNext={async bio => {
              const newData = { ...onboardingData, bio };
              updateData({ bio });
              await saveOnboardingProgress(6, newData);
              nextStep();
            }}
            onBack={prevStep}
          />
        );
      case 6:
        return (
          <BusinessSocialMediaStep
            value={onboardingData.socialMedia}
            onNext={async socialMedia => {
              const newData = { ...onboardingData, socialMedia };
              updateData({ socialMedia });
              await saveOnboardingProgress(7, newData);
              nextStep();
            }}
            onBack={prevStep}
          />
        );
      case 7:
        return (
          <CityStep
            value={onboardingData.city}
            onChange={city => updateData({ city })}
            onNext={async city => {
              const newData = { ...onboardingData, city };
              updateData({ city });
              await saveOnboardingProgress(7, newData);
              completeOnboarding();
            }}
            onBack={prevStep}
            isLastStep={true}
            isLoading={isLoading}
            title="U kom gradu se nalazi vaša firma?"
            description="Ovo polje je opcionalno, ali pomaže influencerima da pronađu lokalne brendove"
          />
        );
      default:
        return null;
    }
  };

  const renderCurrentStep = () => {
    return renderStep();
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  if (!user) {
    return null;
  }

  const progressPercentage = ((currentStep - 1) / (TOTAL_STEPS - 1)) * 100;

  return (
    <div className="min-h-screen bg-instagram-subtle relative overflow-hidden flex flex-col">
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5"></div>
      <div className="absolute top-10 right-10 w-64 h-64 bg-white/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-10 left-10 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>

      {/* Navbar */}
      <nav className="relative z-10 border-b border-white/20 backdrop-blur-sm bg-white/10 px-4 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/30">
              <span className="text-white font-bold text-lg">🔗</span>
            </div>
            <span className="text-xl font-bold text-white">InfluConnect</span>
          </div>

          {/* Logout Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleSignOut}
            className="flex items-center space-x-2 border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
          >
            <LogOut className="h-4 w-4" />
            <span>Odjava</span>
          </Button>
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative z-10 flex-1 flex items-center justify-center px-4 py-8">
        <div className="w-full max-w-2xl">
          {/* Progress bar */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-white">
                Korak {currentStep} od {TOTAL_STEPS}
              </span>
              <span className="text-sm text-white/70">
                {Math.round(progressPercentage)}%
              </span>
            </div>
            <Progress value={progressPercentage} className="h-2 bg-white/20" />
          </div>

          {/* Main content */}
          <Card className="glass-instagram border-white/20 shadow-2xl">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl text-white">
                Kreiranje profila - Biznis
              </CardTitle>
              <p className="text-white/70 mt-2">
                {ONBOARDING_STEPS[currentStep - 1]?.description}
              </p>
            </CardHeader>
            <CardContent>{renderCurrentStep()}</CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
