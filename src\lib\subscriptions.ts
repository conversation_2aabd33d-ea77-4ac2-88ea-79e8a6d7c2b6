import { supabase } from './supabase';

export type SubscriptionType = 'free' | 'premium';

export interface SubscriptionLimits {
  activeCampaigns: number;
  canViewApplications: boolean;
  canViewApplicationDetails: boolean;
}

export const SUBSCRIPTION_LIMITS = {
  free: {
    business: {
      activeCampaigns: 1,
      canViewApplications: false,
      canViewApplicationDetails: false,
    },
    influencer: {
      monthlyApplications: 5,
      premiumCampaignsAccess: false,
    },
  },
  premium: {
    business: {
      activeCampaigns: -1, // unlimited
      canViewApplications: true,
      canViewApplicationDetails: true,
    },
    influencer: {
      monthlyApplications: -1, // unlimited
      premiumCampaignsAccess: true,
    },
  },
} as const;

export interface CampaignActivationResult {
  canActivate: boolean;
  reason?: string;
  activeCount: number;
  maxAllowed: number;
}

export async function canActivateCampaign(
  businessId: string
): Promise<CampaignActivationResult> {
  try {
    // Get business subscription info
    const { data: business, error } = await supabase
      .from('businesses')
      .select('subscription_type, active_campaigns_count')
      .eq('id', businessId)
      .single();

    if (error || !business) {
      throw new Error('Failed to fetch business data');
    }

    const subscriptionType =
      (business.subscription_type as SubscriptionType) || 'free';
    const limits = SUBSCRIPTION_LIMITS[subscriptionType].business;
    const currentActiveCount = business.active_campaigns_count || 0;

    // If premium (unlimited campaigns)
    if (subscriptionType === 'premium') {
      return {
        canActivate: true,
        activeCount: currentActiveCount,
        maxAllowed: -1,
      };
    }

    // For free users
    const canActivate = currentActiveCount < limits.activeCampaigns;

    return {
      canActivate,
      reason: canActivate ? undefined : 'free_limit_reached',
      activeCount: currentActiveCount,
      maxAllowed: limits.activeCampaigns,
    };
  } catch (error) {
    console.error('Error checking campaign activation:', error);
    return {
      canActivate: false,
      reason: 'error',
      activeCount: 0,
      maxAllowed: 1,
    };
  }
}

export async function activateCampaign(
  campaignId: string,
  businessId: string
): Promise<boolean> {
  try {
    // First check if we can activate
    const activationCheck = await canActivateCampaign(businessId);
    if (!activationCheck.canActivate) {
      return false;
    }

    // Begin transaction-like operations
    // 1. Update campaign status and set activation timestamp
    const { error: campaignError } = await supabase
      .from('campaigns')
      .update({
        status: 'active',
        activated_at: new Date().toISOString(),
        is_marketplace_active: true, // Also enable marketplace visibility
      })
      .eq('id', campaignId);

    if (campaignError) {
      throw campaignError;
    }

    // 2. Increment active campaigns count
    const { error: businessError } = await supabase
      .from('businesses')
      .update({
        active_campaigns_count: activationCheck.activeCount + 1,
      })
      .eq('id', businessId);

    if (businessError) {
      // Rollback campaign status if business update fails
      await supabase
        .from('campaigns')
        .update({
          status: 'draft',
          activated_at: null,
          is_marketplace_active: false,
        })
        .eq('id', campaignId);

      throw businessError;
    }

    return true;
  } catch (error) {
    console.error('Error activating campaign:', error);
    return false;
  }
}

export async function deactivateCampaign(
  campaignId: string,
  businessId: string
): Promise<boolean> {
  try {
    // Get current business active count
    const { data: business } = await supabase
      .from('businesses')
      .select('active_campaigns_count')
      .eq('id', businessId)
      .single();

    // 1. Update campaign status
    const { error: campaignError } = await supabase
      .from('campaigns')
      .update({
        status: 'draft',
        activated_at: null,
      })
      .eq('id', campaignId);

    if (campaignError) {
      throw campaignError;
    }

    // 2. Decrement active campaigns count (ensure it doesn't go below 0)
    const currentCount = business?.active_campaigns_count || 0;
    const { error: businessError } = await supabase
      .from('businesses')
      .update({
        active_campaigns_count: Math.max(0, currentCount - 1),
      })
      .eq('id', businessId);

    if (businessError) {
      throw businessError;
    }

    return true;
  } catch (error) {
    console.error('Error deactivating campaign:', error);
    return false;
  }
}

export async function toggleCampaignMarketplaceVisibility(
  campaignId: string,
  isVisible: boolean
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('campaigns')
      .update({
        is_marketplace_active: isVisible,
      })
      .eq('id', campaignId);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error toggling marketplace visibility:', error);
    return false;
  }
}

export function canViewApplications(
  subscriptionType: SubscriptionType
): boolean {
  return SUBSCRIPTION_LIMITS[subscriptionType].business.canViewApplications;
}

export function canViewApplicationDetails(
  subscriptionType: SubscriptionType
): boolean {
  return SUBSCRIPTION_LIMITS[subscriptionType].business
    .canViewApplicationDetails;
}
