# Database Types Backup

Ovo je backup sadr<PERSON>aj za `src/lib/database.types.ts` fajl:

```typescript
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      business_target_categories: {
        Row: {
          business_id: string
          category_id: number
          created_at: string | null
        }
        Insert: {
          business_id: string
          category_id: number
          created_at?: string | null
        }
        Update: {
          business_id?: string
          category_id?: number
          created_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "business_target_categories_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "business_target_categories_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      businesses: {
        Row: {
          budget_range: string | null
          company_name: string
          company_size: string | null
          created_at: string | null
          id: string
          industry: string | null
          is_verified: boolean | null
          updated_at: string | null
        }
        Insert: {
          budget_range?: string | null
          company_name: string
          company_size?: string | null
          created_at?: string | null
          id: string
          industry?: string | null
          is_verified?: boolean | null
          updated_at?: string | null
        }
        Update: {
          budget_range?: string | null
          company_name?: string
          company_size?: string | null
          created_at?: string | null
          id?: string
          industry?: string | null
          is_verified?: boolean | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "businesses_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_applications: {
        Row: {
          additional_services: string | null
          applied_at: string | null
          audience_insights: string | null
          available_start_date: string | null
          campaign_id: string
          delivery_timeframe: string | null
          experience_relevant: string | null
          id: string
          influencer_id: string
          portfolio_links: string[] | null
          proposal_text: string | null
          proposed_rate: number
          responded_at: string | null
          status: Database["public"]["Enums"]["application_status"] | null
        }
        Insert: {
          additional_services?: string | null
          applied_at?: string | null
          audience_insights?: string | null
          available_start_date?: string | null
          campaign_id: string
          delivery_timeframe?: string | null
          experience_relevant?: string | null
          id?: string
          influencer_id: string
          portfolio_links?: string[] | null
          proposal_text?: string | null
          proposed_rate: number
          responded_at?: string | null
          status?: Database["public"]["Enums"]["application_status"] | null
        }
        Update: {
          additional_services?: string | null
          applied_at?: string | null
          audience_insights?: string | null
          available_start_date?: string | null
          campaign_id?: string
          delivery_timeframe?: string | null
          experience_relevant?: string | null
          id?: string
          influencer_id?: string
          portfolio_links?: string[] | null
          proposal_text?: string | null
          proposed_rate?: number
          responded_at?: string | null
          status?: Database["public"]["Enums"]["application_status"] | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_applications_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_applications_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencer_search_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_applications_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencers"
            referencedColumns: ["id"]
          },
        ]
      }
      campaigns: {
        Row: {
          budget: number | null
          business_id: string
          campaign_goal: string | null
          content_types: Database["public"]["Enums"]["content_type"][]
          created_at: string | null
          deliverables: string | null
          description: string
          end_date: string | null
          id: string
          product_description: string | null
          requirements: string | null
          show_business_name: boolean | null
          start_date: string | null
          status: Database["public"]["Enums"]["campaign_status"] | null
          target_audience: Json | null
          title: string
          updated_at: string | null
        }
        Insert: {
          budget?: number | null
          business_id: string
          campaign_goal?: string | null
          content_types: Database["public"]["Enums"]["content_type"][]
          created_at?: string | null
          deliverables?: string | null
          description: string
          end_date?: string | null
          id?: string
          product_description?: string | null
          requirements?: string | null
          show_business_name?: boolean | null
          start_date?: string | null
          status?: Database["public"]["Enums"]["campaign_status"] | null
          target_audience?: Json | null
          title: string
          updated_at?: string | null
        }
        Update: {
          budget?: number | null
          business_id?: string
          campaign_goal?: string | null
          content_types?: Database["public"]["Enums"]["content_type"][]
          created_at?: string | null
          deliverables?: string | null
          description?: string
          end_date?: string | null
          id?: string
          product_description?: string | null
          requirements?: string | null
          show_business_name?: boolean | null
          start_date?: string | null
          status?: Database["public"]["Enums"]["campaign_status"] | null
          target_audience?: Json | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "campaigns_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
        ]
      }
      categories: {
        Row: {
          created_at: string | null
          description: string | null
          icon: string | null
          id: number
          name: string
          slug: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: number
          name: string
          slug: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: number
          name?: string
          slug?: string
        }
        Relationships: []
      }
      chat_messages: {
        Row: {
          created_at: string | null
          edited_at: string | null
          file_name: string | null
          file_size: number | null
          file_type: string | null
          file_url: string | null
          id: string
          message_text: string | null
          read_at: string | null
          room_id: string
          sender_id: string
          sender_type: string
        }
        Insert: {
          created_at?: string | null
          edited_at?: string | null
          file_name?: string | null
          file_size?: number | null
          file_type?: string | null
          file_url?: string | null
          id?: string
          message_text?: string | null
          read_at?: string | null
          room_id: string
          sender_id: string
          sender_type: string
        }
        Update: {
          created_at?: string | null
          edited_at?: string | null
          file_name?: string | null
          file_size?: number | null
          file_type?: string | null
          file_url?: string | null
          id?: string
          message_text?: string | null
          read_at?: string | null
          room_id?: string
          sender_id?: string
          sender_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "chat_messages_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: false
            referencedRelation: "chat_rooms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
```

**VAŽNO:** Ovaj fajl treba da sadrži još mnogo tabela. Ovo je samo početak. Trebam da dodam:
- profiles (sa gender poljem)
- influencers (sa gender poljem) 
- influencer_categories
- influencer_platforms
- platforms
- content_types
- pricing_packages
- i sve ostale tabele...

Fajl je previše veliki za jedan save. Treba ga kreirati po delovima.
